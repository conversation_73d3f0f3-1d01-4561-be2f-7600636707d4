/**
 * 统一响应格式工具模块
 * 提供标准化的API响应格式和响应处理函数
 */

const { HTTP_STATUS, RESPONSE_MESSAGES } = require('./constants');

/**
 * 获取响应格式配置
 * @returns {string} 响应格式类型
 */
function getResponseFormat() {
    return process.env.API_RESPONSE_FORMAT || 'standard';
}

/**
 * 创建标准响应格式
 * @param {number} code - HTTP状态码 (200-299为成功，400-599为错误)
 * @param {string} message - 响应消息，用于描述操作结果
 * @param {any} [data=null] - 响应数据，可以是对象、数组或基本类型
 * @param {Object} [meta=null] - 元数据信息，包含分页、统计等额外信息
 * @returns {Object} 标准响应对象，格式根据环境配置决定
 * @example
 * // 成功响应
 * createResponse(200, '操作成功', { id: 1, name: '测试' })
 *
 * // 错误响应
 * createResponse(400, '参数错误', null, { field: 'songId' })
 */
function createResponse(code, message, data = null, meta = null) {
    const format = getResponseFormat();

    switch (format) {
    case 'simple':
        // 简化格式：只返回数据和状态
        return {
            成功: code >= 200 && code < 300,
            数据: data
        };

    case 'custom': {
        // 自定义格式：包含更多字段
        return {
            状态: code >= 200 && code < 300 ? '成功' : '错误',
            状态码: code,
            消息: message,
            数据: data,
            元数据: meta,
            时间戳: new Date().toISOString(),
            版本: '1.0.0'
        };
    }

    case 'standard':
    default: {
        // 标准格式：当前使用的格式
        const response = {
            状态码: code,
            消息: message,
            时间戳: new Date().toISOString(),
            数据: data
        };

        // 如果有元数据，添加到响应中
        if (meta) {
            response.元数据 = meta;
        }

        return response;
    }
    }
}

/**
 * 成功响应
 * @param {Object} res - Express响应对象
 * @param {any} data - 响应数据
 * @param {string} message - 自定义消息
 * @param {Object} meta - 元数据
 */
function success(res, data = null, message = RESPONSE_MESSAGES.SUCCESS, meta = null) {
    const response = createResponse(HTTP_STATUS.OK, message, data, meta);
    res.status(HTTP_STATUS.OK).json(response);
}

/**
 * 创建成功响应
 * @param {Object} res - Express响应对象
 * @param {any} data - 响应数据
 * @param {string} message - 自定义消息
 */
function created(res, data = null, message = RESPONSE_MESSAGES.CREATED) {
    const response = createResponse(HTTP_STATUS.CREATED, message, data);
    res.status(HTTP_STATUS.CREATED).json(response);
}

/**
 * 错误响应
 * @param {Object} res - Express响应对象
 * @param {number} statusCode - HTTP状态码
 * @param {string} message - 错误消息
 * @param {string} errorCode - 错误代码
 * @param {any} details - 错误详情
 */
function error(res, statusCode, message, errorCode = null, details = null) {
    const response = createResponse(statusCode, message);
  
    if (errorCode) {
        response.错误代码 = errorCode;
    }

    if (details) {
        response.详情 = details;
    }

    res.status(statusCode).json(response);
}

/**
 * 参数错误响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 * @param {any} details - 错误详情
 */
function badRequest(res, message = RESPONSE_MESSAGES.BAD_REQUEST, details = null) {
    error(res, HTTP_STATUS.BAD_REQUEST, message, 'VALIDATION_ERROR', details);
}

/**
 * 未找到响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 */
function notFound(res, message = RESPONSE_MESSAGES.NOT_FOUND) {
    error(res, HTTP_STATUS.NOT_FOUND, message, 'NOT_FOUND');
}

/**
 * 服务器内部错误响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 * @param {any} details - 错误详情
 */
function internalError(res, message = RESPONSE_MESSAGES.INTERNAL_ERROR, details = null) {
    error(res, HTTP_STATUS.INTERNAL_SERVER_ERROR, message, 'INTERNAL_ERROR', details);
}

/**
 * 服务不可用响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 */
function serviceUnavailable(res, message = RESPONSE_MESSAGES.SERVICE_UNAVAILABLE) {
    error(res, HTTP_STATUS.SERVICE_UNAVAILABLE, message, 'SERVICE_UNAVAILABLE');
}

/**
 * 分页响应
 * @param {Object} res - Express响应对象
 * @param {Array} data - 数据数组
 * @param {number} page - 当前页码
 * @param {number} pageSize - 每页大小
 * @param {number} total - 总数量
 * @param {string} message - 响应消息
 */
function paginated(res, data, page, pageSize, total, message = RESPONSE_MESSAGES.SUCCESS) {
    const meta = {
        pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            total: parseInt(total),
            totalPages: Math.ceil(total / pageSize),
            hasNext: page * pageSize < total,
            hasPrev: page > 1
        }
    };

    success(res, data, message, meta);
}

/**
 * 搜索结果响应
 * @param {Object} res - Express响应对象
 * @param {Array} results - 搜索结果
 * @param {string} query - 搜索查询
 * @param {number} total - 总结果数
 * @param {number} took - 搜索耗时(毫秒)
 */
function searchResults(res, results, query, total, took) {
    const meta = {
        搜索: {
            查询: query,
            总数: total,
            耗时: took,
            数量: results.length
        }
    };

    success(res, results, RESPONSE_MESSAGES.SUCCESS, meta);
}

module.exports = {
    createResponse,
    success,
    created,
    error,
    badRequest,
    notFound,
    internalError,
    serviceUnavailable,
    paginated,
    searchResults
};
