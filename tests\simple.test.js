/**
 * 简化的API测试脚本
 * 快速验证核心功能
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api`;

async function testAPI() {
    console.log('🎵 开始简化API测试\n');
    
    let passedTests = 0;
    let totalTests = 0;

    async function test(name, testFn) {
        totalTests++;
        try {
            await testFn();
            console.log(`✅ ${name}`);
            passedTests++;
        } catch (error) {
            console.log(`❌ ${name}: ${error.message}`);
        }
    }

    // 1. 健康检查
    await test('健康检查', async () => {
        const response = await axios.get(`${BASE_URL}/health`);
        if (response.status !== 200 || response.data.code !== 200) {
            throw new Error('健康检查失败');
        }
    });

    // 2. API信息
    await test('API信息', async () => {
        const response = await axios.get(API_BASE);
        if (response.status !== 200 || !response.data.success) {
            throw new Error('API信息获取失败');
        }
    });

    // 3. API文档
    await test('API文档', async () => {
        const response = await axios.get(`${API_BASE}/docs`);
        if (response.status !== 200 || !response.data.success) {
            throw new Error('API文档获取失败');
        }
    });

    // 4. 歌曲信息
    await test('歌曲信息', async () => {
        const response = await axios.get(`${API_BASE}/song/418602084`);
        if (response.status !== 200 || !response.data.success) {
            throw new Error('歌曲信息获取失败');
        }
    });

    // 5. 歌曲元数据
    await test('歌曲元数据', async () => {
        const response = await axios.get(`${API_BASE}/metadata/418602084`);
        if (response.status !== 200 || !response.data.success) {
            throw new Error('歌曲元数据获取失败');
        }
    });

    // 6. 搜索功能
    await test('搜索功能', async () => {
        const response = await axios.post(`${API_BASE}/search`, {
            type: 'keyword',
            query: '周杰伦',
            page: 1,
            pageSize: 10
        });
        if (response.status !== 200 || !response.data.success) {
            throw new Error('搜索功能失败');
        }
    });

    // 7. ID搜索
    await test('ID搜索', async () => {
        const response = await axios.get(`${API_BASE}/search/id/418602084`);
        if (response.status !== 200 || !response.data.success) {
            throw new Error('ID搜索失败');
        }
    });

    // 8. 关键词搜索
    await test('关键词搜索', async () => {
        const response = await axios.get(`${API_BASE}/search/keyword?q=周杰伦&page=1&pageSize=10`);
        if (response.status !== 200 || !response.data.success) {
            throw new Error('关键词搜索失败');
        }
    });

    // 9. 解锁功能
    await test('解锁功能', async () => {
        const response = await axios.post(`${API_BASE}/unlock`, {
            songIds: [418602084],
            minBitrate: 128000
        });
        if (response.status !== 200 || !response.data.success) {
            throw new Error('解锁功能失败');
        }
    });

    // 10. 音源列表
    await test('音源列表', async () => {
        const response = await axios.get(`${API_BASE}/sources`);
        if (response.status !== 200 || !response.data.success) {
            throw new Error('音源列表获取失败');
        }
    });

    // 11. 错误处理 - 无效ID
    await test('错误处理-无效ID', async () => {
        try {
            await axios.get(`${API_BASE}/song/invalid`);
            throw new Error('应该返回400错误');
        } catch (error) {
            if (error.response && error.response.status === 400) {
                return; // 正确的错误处理
            }
            throw error;
        }
    });

    // 12. 错误处理 - 404
    await test('错误处理-404', async () => {
        try {
            await axios.get(`${API_BASE}/nonexistent`);
            throw new Error('应该返回404错误');
        } catch (error) {
            if (error.response && error.response.status === 404) {
                return; // 正确的错误处理
            }
            throw error;
        }
    });

    // 测试结果
    console.log('\n📊 测试结果:');
    console.log('='.repeat(40));
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${passedTests}`);
    console.log(`失败测试: ${totalTests - passedTests}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(2)}%`);
    console.log('='.repeat(40));

    if (passedTests === totalTests) {
        console.log('\n🎉 所有测试通过！');
        return true;
    } else {
        console.log('\n⚠️ 部分测试失败，需要检查。');
        return false;
    }
}

// 运行测试
if (require.main === module) {
    testAPI()
        .then((success) => {
            process.exit(success ? 0 : 1);
        })
        .catch((error) => {
            console.error('测试过程中发生错误:', error.message);
            process.exit(1);
        });
}

module.exports = testAPI;
