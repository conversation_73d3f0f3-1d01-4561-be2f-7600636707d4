# 🚀 音乐解锁服务部署和维护指南

## 📋 部署前准备

### 系统要求
- **Node.js**: 18.0+ (推荐 18.17.0 LTS)
- **npm**: 9.0+ 或 yarn 1.22+
- **内存**: 最小 512MB，推荐 1GB+
- **磁盘**: 最小 1GB 可用空间
- **网络**: 稳定的互联网连接

### 环境检查
```bash
# 检查Node.js版本
node --version

# 检查npm版本
npm --version

# 检查系统资源
free -h  # Linux
wmic OS get TotalVisibleMemorySize /value  # Windows
```

## 🔧 生产环境部署

### 1. 环境配置
```bash
# 克隆或复制项目文件
cp -r 项目目录 /opt/music-unlock-server

# 进入项目目录
cd /opt/music-unlock-server

# 安装生产依赖
npm ci --only=production

# 复制并配置环境变量
cp .env.example .env
```

### 2. 生产环境配置
编辑 `.env` 文件，设置生产环境参数：

```bash
# 服务配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# 安全配置
CORS_ORIGIN=https://yourdomain.com,https://www.yourdomain.com
SESSION_SECRET=your-super-secure-random-string-here
MAX_REQUEST_SIZE=10mb

# 性能配置
BATCH_CONCURRENCY=3
REQUEST_TIMEOUT=30000
UNLOCK_TIMEOUT=25000

# 日志配置
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=false

# 频率限制
RATE_LIMIT_MAX_REQUESTS=50
RATE_LIMIT_WINDOW_MS=900000
```

### 3. 使用PM2部署（推荐）
```bash
# 全局安装PM2
npm install -g pm2

# 创建PM2配置文件
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'music-unlock-server',
    script: 'src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    max_memory_restart: '500M',
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'test*'],
    restart_delay: 4000
  }]
};
EOF

# 启动服务
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

### 4. 使用Docker部署
```bash
# 创建Dockerfile
cat > Dockerfile << 'EOF'
FROM node:18-alpine

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . .

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 设置权限
RUN chown -R nodejs:nodejs /app
USER nodejs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动命令
CMD ["node", "src/app.js"]
EOF

# 构建镜像
docker build -t music-unlock-server:latest .

# 运行容器
docker run -d \
  --name music-unlock-server \
  --restart unless-stopped \
  -p 3000:3000 \
  -v $(pwd)/.env:/app/.env:ro \
  -v $(pwd)/logs:/app/logs \
  music-unlock-server:latest
```

## 🔍 监控和维护

### 1. 服务监控
```bash
# PM2监控
pm2 monit

# 查看服务状态
pm2 status

# 查看日志
pm2 logs music-unlock-server

# 重启服务
pm2 restart music-unlock-server

# 停止服务
pm2 stop music-unlock-server
```

### 2. 健康检查脚本
```bash
#!/bin/bash
# health-check.sh

SERVICE_URL="http://localhost:3000"
LOG_FILE="/var/log/music-unlock-health.log"

# 检查服务响应
response=$(curl -s -o /dev/null -w "%{http_code}" $SERVICE_URL)

if [ $response -eq 200 ]; then
    echo "$(date): Service is healthy" >> $LOG_FILE
    exit 0
else
    echo "$(date): Service is unhealthy (HTTP $response)" >> $LOG_FILE
    # 可以添加重启逻辑
    # pm2 restart music-unlock-server
    exit 1
fi
```

### 3. 日志轮转配置
```bash
# 创建logrotate配置
sudo tee /etc/logrotate.d/music-unlock-server << 'EOF'
/opt/music-unlock-server/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 nodejs nodejs
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
```

## 🔒 安全最佳实践

### 1. 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 3000/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

### 2. 反向代理配置（Nginx）
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
```

### 3. SSL证书配置
```bash
# 使用Let's Encrypt
sudo certbot --nginx -d yourdomain.com
```

## 📊 性能优化

### 1. 系统级优化
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化TCP参数
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65536" >> /etc/sysctl.conf
sysctl -p
```

### 2. Node.js优化
```bash
# 设置Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=1024"

# 启用生产模式优化
export NODE_ENV=production
```

## 🚨 故障排除

### 常见问题和解决方案

#### 1. 服务无法启动
```bash
# 检查端口占用
netstat -tulpn | grep :3000
lsof -i :3000

# 检查配置文件
node scripts/validate-config.js

# 查看详细错误日志
pm2 logs music-unlock-server --lines 50
```

#### 2. 内存使用过高
```bash
# 监控内存使用
pm2 monit

# 重启服务释放内存
pm2 restart music-unlock-server

# 调整PM2配置中的max_memory_restart
```

#### 3. API响应缓慢
```bash
# 检查网络连接
ping music.163.com
ping y.qq.com

# 调整超时配置
# 在.env中减少REQUEST_TIMEOUT和UNLOCK_TIMEOUT值
```

## 📈 监控指标

### 关键指标监控
- **响应时间**: < 5秒 (正常)
- **内存使用**: < 500MB (正常)
- **CPU使用**: < 50% (正常)
- **错误率**: < 5% (正常)
- **可用性**: > 99% (目标)

### 监控脚本示例
```bash
#!/bin/bash
# monitor.sh

# 获取服务状态
STATUS=$(pm2 jlist | jq -r '.[] | select(.name=="music-unlock-server") | .pm2_env.status')

# 获取内存使用
MEMORY=$(pm2 jlist | jq -r '.[] | select(.name=="music-unlock-server") | .monit.memory')

# 记录到监控日志
echo "$(date): Status=$STATUS, Memory=${MEMORY}B" >> /var/log/music-unlock-monitor.log

# 发送到监控系统（可选）
# curl -X POST "http://monitoring-system/api/metrics" \
#   -d "service=music-unlock-server&status=$STATUS&memory=$MEMORY"
```

## 🔄 更新和维护

### 1. 版本更新流程
```bash
# 备份当前版本
cp -r /opt/music-unlock-server /opt/music-unlock-server.backup

# 更新代码
git pull origin main

# 安装新依赖
npm ci --only=production

# 验证配置
node scripts/validate-config.js

# 重启服务
pm2 restart music-unlock-server

# 验证服务
node final-verification.js
```

### 2. 定期维护任务
```bash
# 每日任务
0 2 * * * /opt/music-unlock-server/scripts/health-check.sh

# 每周任务
0 3 * * 0 /opt/music-unlock-server/scripts/cleanup-logs.sh

# 每月任务
0 4 1 * * /opt/music-unlock-server/scripts/update-dependencies.sh
```

---

## 📞 技术支持

### 日志位置
- **应用日志**: `./logs/app-*.log`
- **PM2日志**: `./logs/pm2-*.log`
- **系统日志**: `/var/log/music-unlock-*.log`

### 配置验证
```bash
# 验证配置有效性
node scripts/validate-config.js

# 测试服务功能
node final-verification.js
```

### 紧急恢复
```bash
# 快速重启
pm2 restart music-unlock-server

# 回滚到备份版本
mv /opt/music-unlock-server.backup /opt/music-unlock-server
pm2 restart music-unlock-server
```

这份指南涵盖了生产环境部署的所有关键方面，确保服务的稳定运行和高可用性。
