#!/usr/bin/env node

/**
 * FOLLOW_SOURCE_ORDER 配置行为验证脚本
 * 
 * 测试目标：
 * 1. 验证 FOLLOW_SOURCE_ORDER=false 时的并行行为
 * 2. 验证 FOLLOW_SOURCE_ORDER=true 时的顺序行为
 * 3. 分析音质选择策略
 * 4. 对比两种模式的性能差异
 */

const fs = require('fs');
const http = require('http');
const { spawn } = require('child_process');

// 测试配置
const TEST_SONG_ID = '418602084'; // 周杰伦 - 稻香
const TEST_SOURCES = 'migu,kuwo,qq,kugou'; // 测试音源
const BASE_URL = 'http://localhost:50091';
const TEST_ROUNDS = 5; // 每种配置测试轮数

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    magenta: '\x1b[35m'
};

function colorLog(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试结果存储
const testResults = {
    parallel: [], // FOLLOW_SOURCE_ORDER=false 的结果
    sequential: [] // FOLLOW_SOURCE_ORDER=true 的结果
};

/**
 * 发送HTTP请求
 */
function makeRequest(url) {
    return new Promise((resolve, reject) => {
        const startTime = Date.now();
        const request = http.get(url, (response) => {
            let data = '';
            response.on('data', chunk => data += chunk);
            response.on('end', () => {
                const duration = Date.now() - startTime;
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        statusCode: response.statusCode,
                        data: jsonData,
                        duration: duration
                    });
                } catch (error) {
                    resolve({
                        statusCode: response.statusCode,
                        data: data,
                        duration: duration,
                        parseError: error.message
                    });
                }
            });
        });
        
        request.on('error', reject);
        request.setTimeout(30000, () => {
            request.destroy();
            reject(new Error('Request timeout'));
        });
    });
}

/**
 * 修改环境变量配置
 */
function updateEnvConfig(followSourceOrder) {
    const envPath = '.env';
    let envContent = '';
    
    if (fs.existsSync(envPath)) {
        envContent = fs.readFileSync(envPath, 'utf8');
    } else if (fs.existsSync('.env.example')) {
        envContent = fs.readFileSync('.env.example', 'utf8');
    } else {
        throw new Error('未找到 .env 或 .env.example 文件');
    }
    
    // 更新 FOLLOW_SOURCE_ORDER 配置
    const lines = envContent.split('\n');
    let updated = false;
    
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].startsWith('FOLLOW_SOURCE_ORDER=')) {
            lines[i] = `FOLLOW_SOURCE_ORDER=${followSourceOrder}`;
            updated = true;
            break;
        }
    }
    
    if (!updated) {
        lines.push(`FOLLOW_SOURCE_ORDER=${followSourceOrder}`);
    }
    
    fs.writeFileSync(envPath, lines.join('\n'));
    colorLog(`✅ 已更新配置: FOLLOW_SOURCE_ORDER=${followSourceOrder}`, 'green');
}

/**
 * 启动服务
 */
function startService() {
    return new Promise((resolve, reject) => {
        colorLog('🚀 启动服务...', 'blue');
        
        const serviceProcess = spawn('node', ['src/app.js'], {
            stdio: 'pipe',
            env: { ...process.env, NODE_ENV: 'development' }
        });
        
        let output = '';
        serviceProcess.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        serviceProcess.stderr.on('data', (data) => {
            output += data.toString();
        });
        
        // 等待服务启动
        const checkService = setInterval(async () => {
            try {
                const response = await makeRequest(`${BASE_URL}/`);
                if (response.statusCode === 200) {
                    clearInterval(checkService);
                    colorLog('✅ 服务启动成功', 'green');
                    resolve(serviceProcess);
                }
            } catch (error) {
                // 继续等待
            }
        }, 1000);
        
        // 超时处理
        setTimeout(() => {
            clearInterval(checkService);
            reject(new Error('服务启动超时'));
        }, 15000);
    });
}

/**
 * 停止服务
 */
function stopService(serviceProcess) {
    return new Promise((resolve) => {
        if (serviceProcess) {
            serviceProcess.kill('SIGTERM');
            setTimeout(() => {
                if (!serviceProcess.killed) {
                    serviceProcess.kill('SIGKILL');
                }
                resolve();
            }, 2000);
        } else {
            resolve();
        }
    });
}

/**
 * 测试音乐解锁
 */
async function testUnlock(testName) {
    const results = [];
    
    colorLog(`\n🎵 开始测试: ${testName}`, 'cyan');
    
    for (let round = 1; round <= TEST_ROUNDS; round++) {
        colorLog(`   第 ${round} 轮测试...`, 'yellow');
        
        try {
            const url = `${BASE_URL}/music/unlock?sources=${TEST_SOURCES}&songs=${TEST_SONG_ID}`;
            const response = await makeRequest(url);
            
            if (response.statusCode === 200 && response.data.成功列表) {
                const result = response.data.成功列表;
                const testResult = {
                    round: round,
                    success: true,
                    duration: response.duration,
                    source: result.音源ID,
                    sourceName: result.音源名称,
                    bitrate: result.音质,
                    quality: result.音质描述,
                    fileSize: result.文件大小,
                    format: result.格式,
                    priority: result.优先级 || 0
                };
                
                results.push(testResult);
                colorLog(`     ✅ 成功 - 音源: ${result.音源ID}, 音质: ${result.音质}bps, 耗时: ${response.duration}ms`, 'green');
            } else {
                const testResult = {
                    round: round,
                    success: false,
                    duration: response.duration,
                    error: response.data.消息 || '解锁失败'
                };
                
                results.push(testResult);
                colorLog(`     ❌ 失败 - ${testResult.error}`, 'red');
            }
        } catch (error) {
            const testResult = {
                round: round,
                success: false,
                duration: 0,
                error: error.message
            };
            
            results.push(testResult);
            colorLog(`     ❌ 错误 - ${error.message}`, 'red');
        }
        
        // 间隔1秒避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return results;
}

/**
 * 分析测试结果
 */
function analyzeResults(parallelResults, sequentialResults) {
    colorLog('\n📊 测试结果分析', 'cyan');
    
    // 并行模式分析
    const parallelSuccess = parallelResults.filter(r => r.success);
    const parallelSources = parallelSuccess.map(r => r.source);
    const parallelSourceCounts = {};
    parallelSources.forEach(source => {
        parallelSourceCounts[source] = (parallelSourceCounts[source] || 0) + 1;
    });
    
    colorLog('\n🔄 并行模式 (FOLLOW_SOURCE_ORDER=false):', 'blue');
    colorLog(`   成功率: ${parallelSuccess.length}/${parallelResults.length} (${(parallelSuccess.length/parallelResults.length*100).toFixed(1)}%)`, 'green');
    colorLog(`   平均耗时: ${parallelSuccess.length > 0 ? Math.round(parallelSuccess.reduce((sum, r) => sum + r.duration, 0) / parallelSuccess.length) : 0}ms`, 'yellow');
    colorLog('   音源使用分布:', 'yellow');
    Object.entries(parallelSourceCounts).forEach(([source, count]) => {
        colorLog(`     ${source}: ${count}次 (${(count/parallelSuccess.length*100).toFixed(1)}%)`, 'white');
    });
    
    // 顺序模式分析
    const sequentialSuccess = sequentialResults.filter(r => r.success);
    const sequentialSources = sequentialSuccess.map(r => r.source);
    const sequentialSourceCounts = {};
    sequentialSources.forEach(source => {
        sequentialSourceCounts[source] = (sequentialSourceCounts[source] || 0) + 1;
    });
    
    colorLog('\n📋 顺序模式 (FOLLOW_SOURCE_ORDER=true):', 'blue');
    colorLog(`   成功率: ${sequentialSuccess.length}/${sequentialResults.length} (${(sequentialSuccess.length/sequentialResults.length*100).toFixed(1)}%)`, 'green');
    colorLog(`   平均耗时: ${sequentialSuccess.length > 0 ? Math.round(sequentialSuccess.reduce((sum, r) => sum + r.duration, 0) / sequentialSuccess.length) : 0}ms`, 'yellow');
    colorLog('   音源使用分布:', 'yellow');
    Object.entries(sequentialSourceCounts).forEach(([source, count]) => {
        colorLog(`     ${source}: ${count}次 (${(count/sequentialSuccess.length*100).toFixed(1)}%)`, 'white');
    });
    
    // 音质对比
    colorLog('\n🎵 音质对比:', 'magenta');
    const parallelBitrates = parallelSuccess.map(r => r.bitrate).filter(b => b > 0);
    const sequentialBitrates = sequentialSuccess.map(r => r.bitrate).filter(b => b > 0);
    
    if (parallelBitrates.length > 0) {
        const avgParallelBitrate = Math.round(parallelBitrates.reduce((sum, b) => sum + b, 0) / parallelBitrates.length);
        const maxParallelBitrate = Math.max(...parallelBitrates);
        colorLog(`   并行模式 - 平均音质: ${avgParallelBitrate}bps, 最高音质: ${maxParallelBitrate}bps`, 'white');
    }
    
    if (sequentialBitrates.length > 0) {
        const avgSequentialBitrate = Math.round(sequentialBitrates.reduce((sum, b) => sum + b, 0) / sequentialBitrates.length);
        const maxSequentialBitrate = Math.max(...sequentialBitrates);
        colorLog(`   顺序模式 - 平均音质: ${avgSequentialBitrate}bps, 最高音质: ${maxSequentialBitrate}bps`, 'white');
    }
    
    // 结论
    colorLog('\n🎯 测试结论:', 'cyan');
    
    // 音源选择可预测性
    const parallelUniqueSources = Object.keys(parallelSourceCounts).length;
    const sequentialUniqueSources = Object.keys(sequentialSourceCounts).length;
    
    if (parallelUniqueSources > 1) {
        colorLog('   ✅ 并行模式确实会使用不同音源，音源选择不可预测', 'green');
    } else {
        colorLog('   ⚠️  并行模式在此测试中只使用了一个音源', 'yellow');
    }
    
    if (sequentialUniqueSources === 1) {
        colorLog('   ✅ 顺序模式使用固定音源，音源选择可预测', 'green');
    } else {
        colorLog('   ⚠️  顺序模式在此测试中使用了多个音源', 'yellow');
    }
    
    // 性能对比
    const parallelAvgTime = parallelSuccess.length > 0 ? parallelSuccess.reduce((sum, r) => sum + r.duration, 0) / parallelSuccess.length : 0;
    const sequentialAvgTime = sequentialSuccess.length > 0 ? sequentialSuccess.reduce((sum, r) => sum + r.duration, 0) / sequentialSuccess.length : 0;
    
    if (parallelAvgTime > 0 && sequentialAvgTime > 0) {
        if (parallelAvgTime < sequentialAvgTime) {
            const speedup = ((sequentialAvgTime - parallelAvgTime) / sequentialAvgTime * 100).toFixed(1);
            colorLog(`   ✅ 并行模式比顺序模式快 ${speedup}%`, 'green');
        } else {
            const slowdown = ((parallelAvgTime - sequentialAvgTime) / parallelAvgTime * 100).toFixed(1);
            colorLog(`   ⚠️  顺序模式比并行模式快 ${slowdown}%`, 'yellow');
        }
    }
    
    return {
        parallel: {
            successRate: parallelSuccess.length / parallelResults.length,
            avgDuration: parallelAvgTime,
            sourceCounts: parallelSourceCounts,
            avgBitrate: parallelBitrates.length > 0 ? parallelBitrates.reduce((sum, b) => sum + b, 0) / parallelBitrates.length : 0
        },
        sequential: {
            successRate: sequentialSuccess.length / sequentialResults.length,
            avgDuration: sequentialAvgTime,
            sourceCounts: sequentialSourceCounts,
            avgBitrate: sequentialBitrates.length > 0 ? sequentialBitrates.reduce((sum, b) => sum + b, 0) / sequentialBitrates.length : 0
        }
    };
}

/**
 * 生成测试报告
 */
function generateReport(analysis) {
    const report = {
        title: 'FOLLOW_SOURCE_ORDER 配置行为验证报告',
        timestamp: new Date().toISOString(),
        testConfig: {
            songId: TEST_SONG_ID,
            sources: TEST_SOURCES,
            rounds: TEST_ROUNDS
        },
        results: {
            parallel: testResults.parallel,
            sequential: testResults.sequential
        },
        analysis: analysis,
        conclusions: []
    };
    
    // 生成结论
    if (analysis.parallel.sourceCounts && Object.keys(analysis.parallel.sourceCounts).length > 1) {
        report.conclusions.push('并行模式 (FOLLOW_SOURCE_ORDER=false) 确实会使用不同音源，音源选择不可预测');
    }
    
    if (analysis.sequential.sourceCounts && Object.keys(analysis.sequential.sourceCounts).length === 1) {
        report.conclusions.push('顺序模式 (FOLLOW_SOURCE_ORDER=true) 使用固定音源，音源选择可预测');
    }
    
    if (analysis.parallel.avgDuration > 0 && analysis.sequential.avgDuration > 0) {
        if (analysis.parallel.avgDuration < analysis.sequential.avgDuration) {
            const speedup = ((analysis.sequential.avgDuration - analysis.parallel.avgDuration) / analysis.sequential.avgDuration * 100).toFixed(1);
            report.conclusions.push(`并行模式比顺序模式快 ${speedup}%`);
        }
    }
    
    // 关于音质选择的结论
    if (analysis.parallel.avgBitrate > 0 && analysis.sequential.avgBitrate > 0) {
        if (Math.abs(analysis.parallel.avgBitrate - analysis.sequential.avgBitrate) < 10000) {
            report.conclusions.push('两种模式的平均音质相近，没有明显的音质优先选择差异');
        } else if (analysis.parallel.avgBitrate > analysis.sequential.avgBitrate) {
            report.conclusions.push('并行模式获得了更高的平均音质');
        } else {
            report.conclusions.push('顺序模式获得了更高的平均音质');
        }
    }
    
    const reportFile = `follow-source-order-test-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    colorLog(`\n📄 详细测试报告已保存到: ${reportFile}`, 'blue');
    
    return reportFile;
}

/**
 * 主测试函数
 */
async function main() {
    colorLog('🔍 FOLLOW_SOURCE_ORDER 配置行为验证测试', 'cyan');
    colorLog('='.repeat(50), 'cyan');
    
    let serviceProcess = null;
    
    try {
        // 测试1: 并行模式 (FOLLOW_SOURCE_ORDER=false)
        colorLog('\n📋 第一阶段: 测试并行模式', 'magenta');
        updateEnvConfig('false');
        serviceProcess = await startService();
        testResults.parallel = await testUnlock('并行模式 (FOLLOW_SOURCE_ORDER=false)');
        await stopService(serviceProcess);
        
        // 等待服务完全停止
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 测试2: 顺序模式 (FOLLOW_SOURCE_ORDER=true)
        colorLog('\n📋 第二阶段: 测试顺序模式', 'magenta');
        updateEnvConfig('true');
        serviceProcess = await startService();
        testResults.sequential = await testUnlock('顺序模式 (FOLLOW_SOURCE_ORDER=true)');
        await stopService(serviceProcess);
        
        // 分析结果
        const analysis = analyzeResults(testResults.parallel, testResults.sequential);
        
        // 生成报告
        generateReport(analysis);
        
        // 恢复原始配置
        updateEnvConfig('false');
        
        colorLog('\n🎉 测试完成！', 'green');
        
    } catch (error) {
        colorLog(`\n❌ 测试过程中发生错误: ${error.message}`, 'red');
        
        if (serviceProcess) {
            await stopService(serviceProcess);
        }
        
        // 恢复原始配置
        try {
            updateEnvConfig('false');
        } catch (restoreError) {
            colorLog(`⚠️  恢复配置失败: ${restoreError.message}`, 'yellow');
        }
        
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    main().catch(error => {
        colorLog(`\n💥 未处理的错误: ${error.message}`, 'red');
        process.exit(1);
    });
}

module.exports = {
    testUnlock,
    analyzeResults,
    generateReport
};
