/**
 * 简化的HTML页面E2E测试
 * 专注于基本功能验证
 */

const { test, expect } = require('@playwright/test');

// 测试配置
const BASE_URL = 'http://localhost:3000';

test.describe('音乐解锁服务基础功能测试', () => {
    test.beforeEach(async ({ page }) => {
        // 访问测试页面
        await page.goto(BASE_URL);
        
        // 等待页面加载完成
        await page.waitForLoadState('networkidle');
    });

    test('页面基本加载测试', async ({ page }) => {
        // 检查页面标题
        await expect(page).toHaveTitle(/音乐解锁服务/);
        
        // 检查页面主要内容是否存在
        await expect(page.locator('h1')).toContainText('音乐解锁服务');
        
        // 检查导航标签是否存在
        await expect(page.locator('.nav-tabs')).toBeVisible();
        
        // 检查标签按钮
        await expect(page.locator('button[data-tab="song"]')).toBeVisible();
        await expect(page.locator('button[data-tab="search"]')).toBeVisible();
        await expect(page.locator('button[data-tab="unlock"]')).toBeVisible();
        await expect(page.locator('button[data-tab="sources"]')).toBeVisible();
    });

    test('标签页切换功能', async ({ page }) => {
        // 测试搜索标签页
        await page.click('button[data-tab="search"]');
        await expect(page.locator('#search-tab')).toBeVisible();
        
        // 测试解锁标签页
        await page.click('button[data-tab="unlock"]');
        await expect(page.locator('#unlock-tab')).toBeVisible();
        
        // 测试音源标签页
        await page.click('button[data-tab="sources"]');
        await expect(page.locator('#sources-tab')).toBeVisible();
        
        // 回到歌曲标签页
        await page.click('button[data-tab="song"]');
        await expect(page.locator('#song-tab')).toBeVisible();
    });

    test('歌曲信息输入框测试', async ({ page }) => {
        // 确保在歌曲标签页
        await page.click('button[data-tab="song"]');
        
        // 检查输入框是否存在
        await expect(page.locator('#song-id')).toBeVisible();
        
        // 测试输入功能
        await page.fill('#song-id', '418602084');
        const inputValue = await page.locator('#song-id').inputValue();
        expect(inputValue).toBe('418602084');
        
        // 检查按钮是否存在
        await expect(page.locator('button:has-text("获取歌曲信息")')).toBeVisible();
    });

    test('搜索功能输入框测试', async ({ page }) => {
        // 切换到搜索标签页
        await page.click('button[data-tab="search"]');
        
        // 检查搜索输入框
        await expect(page.locator('#search-query')).toBeVisible();
        
        // 测试输入功能
        await page.fill('#search-query', '周杰伦');
        const searchValue = await page.locator('#search-query').inputValue();
        expect(searchValue).toBe('周杰伦');
        
        // 检查搜索按钮
        await expect(page.locator('button:has-text("开始搜索")')).toBeVisible();
    });

    test('解锁功能输入框测试', async ({ page }) => {
        // 切换到解锁标签页
        await page.click('button[data-tab="unlock"]');
        
        // 检查解锁输入框
        await expect(page.locator('#unlock-ids')).toBeVisible();
        
        // 测试输入功能
        await page.fill('#unlock-ids', '418602084');
        const unlockValue = await page.locator('#unlock-ids').inputValue();
        expect(unlockValue).toBe('418602084');
        
        // 检查解锁按钮
        await expect(page.locator('button:has-text("批量解锁")')).toBeVisible();
    });

    test('音源管理按钮测试', async ({ page }) => {
        // 切换到音源标签页
        await page.click('button[data-tab="sources"]');
        
        // 检查音源管理按钮
        await expect(page.locator('button:has-text("获取音源列表")')).toBeVisible();
        await expect(page.locator('button:has-text("获取音源状态")')).toBeVisible();
        await expect(page.locator('button:has-text("测试所有音源")')).toBeVisible();
    });

    test('服务状态指示器测试', async ({ page }) => {
        // 检查服务状态指示器
        await expect(page.locator('#service-status')).toBeVisible();
        
        // 检查API版本信息
        await expect(page.locator('#api-version')).toBeVisible();
    });

    test('响应式布局测试', async ({ page }) => {
        // 测试不同屏幕尺寸
        await page.setViewportSize({ width: 1200, height: 800 });
        await expect(page.locator('.header')).toBeVisible();
        
        await page.setViewportSize({ width: 768, height: 600 });
        await expect(page.locator('.header')).toBeVisible();
        
        await page.setViewportSize({ width: 480, height: 800 });
        await expect(page.locator('.header')).toBeVisible();
    });

    test('页面性能基础测试', async ({ page }) => {
        const startTime = Date.now();
        
        // 重新加载页面
        await page.reload();
        await page.waitForLoadState('networkidle');
        
        const endTime = Date.now();
        const loadTime = endTime - startTime;
        
        // 检查页面加载时间是否合理（小于5秒）
        expect(loadTime).toBeLessThan(5000);
        console.log(`页面加载时间: ${loadTime}ms`);
    });
});
