/**
 * 音源管理控制器
 * 处理音源相关的API请求
 */

const { asyncHandler } = require('../middleware/errorHandler');
const { logBusiness } = require('../middleware/logger');
const { MUSIC_SOURCES, SOURCE_DISPLAY_NAMES } = require('../utils/constants');
const config = require('../config/config');

/**
 * 获取整合的音源管理信息 - 新的统一API
 * GET /api/sources
 */
const getSources = asyncHandler(async (req, res) => {
    logBusiness('获取整合音源管理信息');

    const currentTime = new Date().toISOString();

    // 获取所有音源信息（包括已禁用的）
    const allSources = Object.values(MUSIC_SOURCES).map(sourceId => {
        const isEnabled = config.music.sources.includes(sourceId);
        const priority = isEnabled ? config.music.sources.indexOf(sourceId) + 1 : 999;

        return {
            优先级: priority,
            音源ID: sourceId,
            音源名称: SOURCE_DISPLAY_NAMES[sourceId] || sourceId,
            需要Cookie: getSourceCookieRequirement(sourceId)
        };
    });

    // 按优先级排序
    allSources.sort((a, b) => a.优先级 - b.优先级);

    // 计算统计数据
    const enabledSources = allSources.filter(s => s.优先级 !== 999);
    const disabledSources = allSources.filter(s => s.优先级 === 999);

    // 构建整合的响应数据
    const integratedResponse = {
        状态码: 200,
        消息: '音源管理服务',
        时间戳: currentTime,
        最后更新: currentTime,
        音源总数: allSources.length,
        已启用音源: enabledSources.length,
        已禁用音源: disabledSources.length,
        遵循音源顺序: config.music.followSourceOrder,
        启用无损: config.music.enableFlac,
        启用本地VIP: config.music.enableLocalVip,
        音源优先级顺序: allSources
    };

    // 直接返回新格式的数据，不使用success包装函数
    res.json(integratedResponse);
});



/**
 * 获取音源是否需要Cookie
 * @param {string} sourceId - 音源ID
 * @returns {boolean} 是否需要Cookie
 */
function getSourceCookieRequirement(sourceId) {
    const cookieRequiredSources = [MUSIC_SOURCES.QQ, MUSIC_SOURCES.JOOX];
    return cookieRequiredSources.includes(sourceId);
}

module.exports = {
    getSources
};
