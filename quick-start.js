#!/usr/bin/env node

/**
 * 音乐解锁服务快速启动和验证脚本
 * 
 * 功能：
 * 1. 检查环境依赖
 * 2. 验证配置文件
 * 3. 启动服务
 * 4. 运行功能验证
 * 5. 生成启动报告
 */

const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');
const http = require('http');

// 颜色输出工具
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorLog(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 步骤计数器
let stepCount = 0;
function nextStep(description) {
    stepCount++;
    colorLog(`\n[步骤 ${stepCount}] ${description}`, 'cyan');
}

// 检查结果存储
const checkResults = {
    environment: false,
    dependencies: false,
    configuration: false,
    service: false,
    api: false
};

// 启动报告数据
const startupReport = {
    startTime: new Date(),
    endTime: null,
    duration: 0,
    steps: [],
    errors: [],
    warnings: [],
    success: false
};

function addStep(step, status, details = '') {
    startupReport.steps.push({
        step,
        status,
        details,
        timestamp: new Date()
    });
}

function addError(error) {
    startupReport.errors.push({
        error,
        timestamp: new Date()
    });
}

function addWarning(warning) {
    startupReport.warnings.push({
        warning,
        timestamp: new Date()
    });
}

// 1. 环境检查
async function checkEnvironment() {
    nextStep('检查运行环境');
    
    try {
        // 检查 Node.js 版本
        const nodeVersion = process.version;
        const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
        
        if (majorVersion >= 16) {
            colorLog(`✅ Node.js 版本: ${nodeVersion} (符合要求)`, 'green');
            checkResults.environment = true;
            addStep('Node.js版本检查', 'success', nodeVersion);
        } else {
            colorLog(`❌ Node.js 版本过低: ${nodeVersion} (需要 >= 16.0.0)`, 'red');
            addStep('Node.js版本检查', 'failed', `版本过低: ${nodeVersion}`);
            addError(`Node.js版本过低: ${nodeVersion}，需要 >= 16.0.0`);
            return false;
        }

        // 检查 npm 版本
        const npmVersion = await new Promise((resolve, reject) => {
            exec('npm --version', (error, stdout) => {
                if (error) reject(error);
                else resolve(stdout.trim());
            });
        });
        
        colorLog(`✅ npm 版本: ${npmVersion}`, 'green');
        addStep('npm版本检查', 'success', npmVersion);
        
        return true;
    } catch (error) {
        colorLog(`❌ 环境检查失败: ${error.message}`, 'red');
        addStep('环境检查', 'failed', error.message);
        addError(`环境检查失败: ${error.message}`);
        return false;
    }
}

// 2. 依赖检查
async function checkDependencies() {
    nextStep('检查项目依赖');
    
    try {
        // 检查 package.json
        if (!fs.existsSync('package.json')) {
            colorLog('❌ 未找到 package.json 文件', 'red');
            addStep('package.json检查', 'failed', '文件不存在');
            addError('未找到 package.json 文件');
            return false;
        }
        
        colorLog('✅ package.json 文件存在', 'green');
        addStep('package.json检查', 'success');
        
        // 检查 node_modules
        if (!fs.existsSync('node_modules')) {
            colorLog('⚠️  node_modules 不存在，正在安装依赖...', 'yellow');
            addWarning('node_modules不存在，需要安装依赖');
            
            await new Promise((resolve, reject) => {
                const npmInstall = spawn('npm', ['install'], { stdio: 'inherit' });
                npmInstall.on('close', (code) => {
                    if (code === 0) {
                        colorLog('✅ 依赖安装完成', 'green');
                        addStep('依赖安装', 'success');
                        resolve();
                    } else {
                        colorLog('❌ 依赖安装失败', 'red');
                        addStep('依赖安装', 'failed', `退出码: ${code}`);
                        addError(`依赖安装失败，退出码: ${code}`);
                        reject(new Error(`npm install failed with code ${code}`));
                    }
                });
            });
        } else {
            colorLog('✅ node_modules 存在', 'green');
            addStep('依赖检查', 'success');
        }
        
        checkResults.dependencies = true;
        return true;
    } catch (error) {
        colorLog(`❌ 依赖检查失败: ${error.message}`, 'red');
        addStep('依赖检查', 'failed', error.message);
        addError(`依赖检查失败: ${error.message}`);
        return false;
    }
}

// 3. 配置验证
async function validateConfiguration() {
    nextStep('验证项目配置');
    
    try {
        // 检查 .env 文件
        if (!fs.existsSync('.env')) {
            if (fs.existsSync('.env.example')) {
                colorLog('⚠️  .env 文件不存在，从 .env.example 复制...', 'yellow');
                fs.copyFileSync('.env.example', '.env');
                colorLog('✅ .env 文件已创建', 'green');
                addStep('.env文件创建', 'success');
                addWarning('.env文件不存在，已从.env.example复制');
            } else {
                colorLog('❌ .env 和 .env.example 文件都不存在', 'red');
                addStep('配置文件检查', 'failed', '.env和.env.example都不存在');
                addError('.env 和 .env.example 文件都不存在');
                return false;
            }
        } else {
            colorLog('✅ .env 文件存在', 'green');
            addStep('.env文件检查', 'success');
        }
        
        // 运行配置验证脚本
        if (fs.existsSync('scripts/validate-config.js')) {
            colorLog('🔍 运行配置验证脚本...', 'blue');
            
            await new Promise((resolve, reject) => {
                const validateProcess = spawn('node', ['scripts/validate-config.js'], { 
                    stdio: 'pipe'
                });
                
                let output = '';
                validateProcess.stdout.on('data', (data) => {
                    output += data.toString();
                });
                
                validateProcess.stderr.on('data', (data) => {
                    output += data.toString();
                });
                
                validateProcess.on('close', (code) => {
                    if (code === 0) {
                        colorLog('✅ 配置验证通过', 'green');
                        addStep('配置验证', 'success');
                        resolve();
                    } else {
                        colorLog('❌ 配置验证失败', 'red');
                        colorLog(output, 'red');
                        addStep('配置验证', 'failed', output);
                        addError(`配置验证失败: ${output}`);
                        reject(new Error('Configuration validation failed'));
                    }
                });
            });
        } else {
            colorLog('⚠️  配置验证脚本不存在，跳过验证', 'yellow');
            addWarning('配置验证脚本不存在');
        }
        
        checkResults.configuration = true;
        return true;
    } catch (error) {
        colorLog(`❌ 配置验证失败: ${error.message}`, 'red');
        addStep('配置验证', 'failed', error.message);
        addError(`配置验证失败: ${error.message}`);
        return false;
    }
}

// 4. 启动服务
async function startService() {
    nextStep('启动音乐解锁服务');
    
    try {
        // 检查端口是否被占用
        const port = process.env.PORT || 50091;
        const isPortFree = await checkPort(port);
        
        if (!isPortFree) {
            colorLog(`⚠️  端口 ${port} 已被占用，尝试终止占用进程...`, 'yellow');
            addWarning(`端口 ${port} 已被占用`);
            
            // 尝试终止占用端口的进程
            try {
                await killProcessOnPort(port);
                colorLog(`✅ 已终止占用端口 ${port} 的进程`, 'green');
                addStep('端口清理', 'success');
            } catch (killError) {
                colorLog(`❌ 无法终止占用端口的进程: ${killError.message}`, 'red');
                addError(`无法终止占用端口的进程: ${killError.message}`);
                return false;
            }
        }
        
        colorLog(`🚀 正在启动服务 (端口: ${port})...`, 'blue');
        
        // 启动服务
        const serviceProcess = spawn('node', ['src/app.js'], {
            stdio: 'pipe',
            env: { ...process.env, NODE_ENV: 'development' }
        });
        
        let serviceOutput = '';
        serviceProcess.stdout.on('data', (data) => {
            serviceOutput += data.toString();
        });
        
        serviceProcess.stderr.on('data', (data) => {
            serviceOutput += data.toString();
        });
        
        // 等待服务启动
        await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('服务启动超时'));
            }, 15000);
            
            const checkService = setInterval(async () => {
                try {
                    const response = await makeRequest(`http://localhost:${port}/`);
                    if (response.statusCode === 200) {
                        clearTimeout(timeout);
                        clearInterval(checkService);
                        colorLog('✅ 服务启动成功', 'green');
                        addStep('服务启动', 'success', `端口: ${port}`);
                        checkResults.service = true;
                        resolve(serviceProcess);
                    }
                } catch (error) {
                    // 继续等待
                }
            }, 1000);
        });
        
        return serviceProcess;
    } catch (error) {
        colorLog(`❌ 服务启动失败: ${error.message}`, 'red');
        addStep('服务启动', 'failed', error.message);
        addError(`服务启动失败: ${error.message}`);
        return null;
    }
}

// 5. API功能验证
async function verifyAPI() {
    nextStep('验证API功能');
    
    try {
        const port = process.env.PORT || 50091;
        const baseUrl = `http://localhost:${port}`;
        
        // 测试服务首页
        colorLog('🔍 测试服务首页...', 'blue');
        const homeResponse = await makeRequest(`${baseUrl}/`);
        if (homeResponse.statusCode === 200) {
            colorLog('✅ 服务首页正常', 'green');
            addStep('服务首页测试', 'success');
        } else {
            throw new Error(`服务首页返回状态码: ${homeResponse.statusCode}`);
        }
        
        // 测试音源管理API
        colorLog('🔍 测试音源管理API...', 'blue');
        const sourceResponse = await makeRequest(`${baseUrl}/music/source`);
        if (sourceResponse.statusCode === 200) {
            colorLog('✅ 音源管理API正常', 'green');
            addStep('音源管理API测试', 'success');
        } else {
            throw new Error(`音源管理API返回状态码: ${sourceResponse.statusCode}`);
        }
        
        // 测试音乐解锁API
        colorLog('🔍 测试音乐解锁API...', 'blue');
        const unlockResponse = await makeRequest(`${baseUrl}/music/unlock?sources=migu&songs=418602084`);
        if (unlockResponse.statusCode === 200) {
            colorLog('✅ 音乐解锁API正常', 'green');
            addStep('音乐解锁API测试', 'success');
        } else {
            throw new Error(`音乐解锁API返回状态码: ${unlockResponse.statusCode}`);
        }
        
        checkResults.api = true;
        return true;
    } catch (error) {
        colorLog(`❌ API验证失败: ${error.message}`, 'red');
        addStep('API验证', 'failed', error.message);
        addError(`API验证失败: ${error.message}`);
        return false;
    }
}

// 6. 生成启动报告
function generateReport() {
    nextStep('生成启动报告');
    
    startupReport.endTime = new Date();
    startupReport.duration = startupReport.endTime - startupReport.startTime;
    startupReport.success = Object.values(checkResults).every(result => result);
    
    const report = {
        title: '🎵 音乐解锁服务快速启动报告',
        timestamp: startupReport.endTime.toISOString(),
        duration: `${Math.round(startupReport.duration / 1000)}秒`,
        success: startupReport.success,
        summary: {
            totalSteps: startupReport.steps.length,
            successSteps: startupReport.steps.filter(s => s.status === 'success').length,
            failedSteps: startupReport.steps.filter(s => s.status === 'failed').length,
            errors: startupReport.errors.length,
            warnings: startupReport.warnings.length
        },
        checkResults,
        steps: startupReport.steps,
        errors: startupReport.errors,
        warnings: startupReport.warnings
    };
    
    // 保存报告到文件
    const reportFile = `startup-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    
    // 显示报告摘要
    colorLog('\n📊 启动报告摘要', 'cyan');
    colorLog(`总耗时: ${report.duration}`, 'blue');
    colorLog(`总步骤: ${report.summary.totalSteps}`, 'blue');
    colorLog(`成功步骤: ${report.summary.successSteps}`, 'green');
    colorLog(`失败步骤: ${report.summary.failedSteps}`, 'red');
    colorLog(`错误数量: ${report.summary.errors}`, 'red');
    colorLog(`警告数量: ${report.summary.warnings}`, 'yellow');
    
    if (report.success) {
        colorLog('\n🎉 启动成功！服务已就绪', 'green');
        colorLog(`访问地址: http://localhost:${process.env.PORT || 50091}`, 'cyan');
    } else {
        colorLog('\n❌ 启动失败，请检查错误信息', 'red');
    }
    
    colorLog(`\n📄 详细报告已保存到: ${reportFile}`, 'blue');
    
    addStep('报告生成', 'success', reportFile);
}

// 工具函数
function checkPort(port) {
    return new Promise((resolve) => {
        const server = require('net').createServer();
        server.listen(port, () => {
            server.once('close', () => resolve(true));
            server.close();
        });
        server.on('error', () => resolve(false));
    });
}

function killProcessOnPort(port) {
    return new Promise((resolve, reject) => {
        const command = process.platform === 'win32' 
            ? `netstat -ano | findstr :${port}` 
            : `lsof -ti:${port}`;
            
        exec(command, (error, stdout) => {
            if (error) {
                reject(error);
                return;
            }
            
            if (process.platform === 'win32') {
                const lines = stdout.split('\n');
                const pids = lines.map(line => {
                    const parts = line.trim().split(/\s+/);
                    return parts[parts.length - 1];
                }).filter(pid => pid && !isNaN(pid));
                
                if (pids.length > 0) {
                    exec(`taskkill /PID ${pids[0]} /F`, (killError) => {
                        if (killError) reject(killError);
                        else resolve();
                    });
                } else {
                    resolve();
                }
            } else {
                const pids = stdout.trim().split('\n').filter(pid => pid);
                if (pids.length > 0) {
                    exec(`kill -9 ${pids.join(' ')}`, (killError) => {
                        if (killError) reject(killError);
                        else resolve();
                    });
                } else {
                    resolve();
                }
            }
        });
    });
}

function makeRequest(url) {
    return new Promise((resolve, reject) => {
        const request = http.get(url, (response) => {
            let data = '';
            response.on('data', chunk => data += chunk);
            response.on('end', () => {
                resolve({
                    statusCode: response.statusCode,
                    data: data
                });
            });
        });
        
        request.on('error', reject);
        request.setTimeout(10000, () => {
            request.destroy();
            reject(new Error('Request timeout'));
        });
    });
}

// 主函数
async function main() {
    colorLog('🎵 音乐解锁服务快速启动脚本', 'bright');
    colorLog('=====================================', 'cyan');
    
    let serviceProcess = null;
    
    try {
        // 执行启动流程
        if (!(await checkEnvironment())) return;
        if (!(await checkDependencies())) return;
        if (!(await validateConfiguration())) return;
        
        serviceProcess = await startService();
        if (!serviceProcess) return;
        
        if (!(await verifyAPI())) return;
        
        // 生成报告
        generateReport();
        
        // 保持服务运行
        colorLog('\n🔄 服务正在运行中...', 'green');
        colorLog('按 Ctrl+C 停止服务', 'yellow');
        
        // 优雅关闭处理
        process.on('SIGINT', () => {
            colorLog('\n🛑 正在停止服务...', 'yellow');
            if (serviceProcess) {
                serviceProcess.kill('SIGTERM');
            }
            process.exit(0);
        });
        
    } catch (error) {
        colorLog(`\n❌ 启动过程中发生错误: ${error.message}`, 'red');
        addError(`启动过程错误: ${error.message}`);
        generateReport();
        
        if (serviceProcess) {
            serviceProcess.kill('SIGTERM');
        }
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        colorLog(`\n💥 未处理的错误: ${error.message}`, 'red');
        process.exit(1);
    });
}

module.exports = {
    checkEnvironment,
    checkDependencies,
    validateConfiguration,
    startService,
    verifyAPI,
    generateReport
};
