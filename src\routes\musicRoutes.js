/**
 * 音乐解锁路由
 * 新的简化API端点
 */

const express = require('express');
const router = express.Router();
const { unlockSong } = require('../services/unlockService');
const { asyncHandler } = require('../middleware/errorHandler');
const { logBusiness, logPerformance } = require('../middleware/logger');
const { validators } = require('../middleware/validator');

/**
 * 根据比特率获取音质描述
 */
function getBitrateDescription(bitrate) {
    if (bitrate >= 999000) return '无损';
    if (bitrate >= 320000) return '高品质';
    if (bitrate >= 192000) return '较高品质';
    if (bitrate >= 128000) return '标准';
    if (bitrate >= 96000) return '普通';
    return '低品质';
}

/**
 * 获取音源名称
 */
function getSourceName(sourceId) {
    const sourceNames = {
        qq: 'QQ音乐',
        kugou: '酷狗音乐',
        kuwo: '酷我音乐',
        migu: '咪咕音乐',
        joox: 'JOOX音乐',
        youtube: 'YouTube Music'
    };
    return sourceNames[sourceId] || sourceId;
}

/**
 * 格式化解锁结果
 */
function formatUnlockResult(result, sourceId, priority) {
    return {
        歌曲ID: result.歌曲ID,
        播放链接: result.播放链接,
        音源ID: sourceId,
        音源名称: getSourceName(sourceId),
        音质: result.音质 || 0,
        音质描述: getBitrateDescription(result.音质 || 0),
        文件大小: result.文件大小 || 0,
        格式: result.格式 || 'mp3',
        优先级: priority,
        解锁时间: new Date().toISOString()
    };
}

/**
 * 解锁单首歌曲
 */
async function unlockSingleSong(songId, sources) {
    const startTime = Date.now();
    
    // 按优先级顺序尝试每个音源
    for (let i = 0; i < sources.length; i++) {
        const source = sources[i];
        const priority = i + 1;
        
        try {
            logBusiness('尝试解锁歌曲', { songId, source, priority });
            
            const result = await unlockSong(songId, [source]);
            
            if (result && result.播放链接) {
                const duration = Date.now() - startTime;
                logPerformance('歌曲解锁成功', { 
                    operation: '单首歌曲解锁',
                    duration: `${duration}ms`,
                    songId,
                    source,
                    success: true
                });
                
                return {
                    success: true,
                    data: formatUnlockResult(result, source, priority)
                };
            }
        } catch (error) {
            logBusiness('音源解锁失败', { songId, source, error: error.message });
            continue; // 尝试下一个音源
        }
    }
    
    // 所有音源都失败
    const duration = Date.now() - startTime;
    logPerformance('歌曲解锁失败', {
        operation: '单首歌曲解锁',
        duration: `${duration}ms`,
        songId,
        success: false
    });
    
    return {
        success: false,
        error: '所有指定音源均无法解锁该歌曲',
        songId
    };
}

/**
 * 新的音乐解锁API端点
 * GET /music/unlock?sources=qq,migu,kuwo&songs=418602084,123456
 */
router.get('/unlock', validators.validateMusicUnlock, asyncHandler(async (req, res) => {
    const startTime = Date.now();

    // 参数已通过validator中间件验证，直接解析使用
    const { sources, songs } = req.query;

    // 解析参数（validator已确保格式正确）
    const sourcesArray = sources.split(',').map(s => s.trim()).filter(s => s);
    const songsArray = songs.split(',').map(s => s.trim()).filter(s => s);

    // 转换歌曲ID为数字（validator已确保格式正确）
    const songIds = songsArray.map(id => parseInt(id, 10));
    
    logBusiness('音乐解锁API请求', {
        sources: sourcesArray,
        songs: songIds,
        count: songIds.length
    });
    
    // 并行处理所有歌曲
    const results = await Promise.all(
        songIds.map(songId => unlockSingleSong(songId, sourcesArray))
    );
    
    // 分类结果
    const successResults = results.filter(r => r.success).map(r => r.data);
    const failureResults = results.filter(r => !r.success).map(r => ({
        歌曲ID: r.songId,
        失败原因: r.error,
        尝试音源: sourcesArray,
        失败时间: new Date().toISOString()
    }));
    
    // 计算统计信息
    const totalCount = songIds.length;
    const successCount = successResults.length;
    const failureCount = failureResults.length;
    const successRate = totalCount > 0 ? ((successCount / totalCount) * 100).toFixed(1) + '%' : '0%';
    
    // 构建响应
    const responseData = {
        状态码: 200,
        消息: totalCount === 1 ? '单首歌曲解锁完成' : '批量解锁完成',
        时间戳: new Date().toISOString(),
        解锁总数: totalCount,
        解锁成功: successCount,
        解锁失败: failureCount,
        解锁成功率: successRate,
        成功列表: totalCount === 1 ? (successResults[0] || {}) : successResults,
        失败列表: failureResults
    };
    
    // 记录性能
    const duration = Date.now() - startTime;
    logPerformance('音乐解锁API完成', {
        operation: '批量音乐解锁',
        duration: `${duration}ms`,
        totalCount,
        successCount,
        failureCount,
        successRate
    });
    
    res.json(responseData);
}));

module.exports = router;
