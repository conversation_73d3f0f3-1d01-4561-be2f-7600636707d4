<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐解锁服务 - 独立API测试工具</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎵</text></svg>">
    <style>
        /* 音乐解锁服务 - 内联样式 (P2硬编码优化) */

        /* CSS变量定义 - 支持动态主题配置 */
        :root {
            --primary-color: #4299e1;
            --secondary-color: #718096;
            --accent-color: #2196f3;
            --success-color: #48bb78;
            --error-color: #f56565;
            --warning-color: #ed8936;
            --max-width: 1200px;
            --header-font-size: 2.5em;
            --base-font-size: 1em;
        }

        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            /* 防止页面闪烁 */
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        /* 页面加载完成后显示 */
        body.loaded {
            opacity: 1;
        }

        .container {
            max-width: var(--max-width);
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin-top: 20px;
            margin-bottom: 20px;
        }

        /* 服务器配置区域 (P2硬编码优化) */
        .server-config {
            background: #e3f2fd;
            border: 2px solid var(--accent-color);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .server-config h3 {
            color: #1976d2;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .server-config .form-group {
            margin-bottom: 15px;
        }

        .server-config input {
            width: 100%;
            padding: 12px;
            border: 2px solid #bbdefb;
            border-radius: 8px;
            font-size: 1em;
            background: white;
        }

        .server-config input:focus {
            outline: none;
            border-color: #2196f3;
            box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
        }

        .server-config .btn {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9em;
            margin-right: 10px;
        }

        .server-config .btn:hover {
            background: #1976d2;
        }

        /* 页面头部 */
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px 0;
            border-bottom: 2px solid #f0f0f0;
        }

        .header h1 {
            font-size: 2.5em;
            color: #4a5568;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .subtitle {
            font-size: 1.1em;
            color: #718096;
            margin-bottom: 15px;
        }

        .status-bar {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .status-indicator {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .status-indicator.online {
            background: var(--success-color);
            color: white;
        }

        .status-indicator.offline {
            background: var(--error-color);
            color: white;
        }

        .status-indicator.checking {
            background: var(--warning-color);
            color: white;
        }

        /* 初始状态，防止闪烁 */
        .status-indicator.initial {
            background: #e2e8f0;
            color: var(--secondary-color);
        }

        .version-info {
            background: var(--primary-color);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        /* API描述样式 */
        .api-description {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-size: 0.95em;
            color: #37474f;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .api-description strong {
            color: #1976d2;
            font-weight: 600;
        }

        /* 导航标签 */
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #e2e8f0;
            flex-wrap: wrap;
            gap: 5px;
        }

        .tab-btn {
            padding: 12px 24px;
            border: none;
            background: transparent;
            color: #718096;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            border-radius: 8px 8px 0 0;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-btn:hover {
            background: #f7fafc;
            color: #4a5568;
        }

        .tab-btn.active {
            background: #4299e1;
            color: white;
            box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
        }

        /* 内容区域 */
        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease-in-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .section h2 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-left: 4px solid #4299e1;
            padding-left: 15px;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #4a5568;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            background: #f7fafc;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4299e1;
            background: white;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .form-group small {
            display: block;
            margin-top: 5px;
            color: #718096;
            font-size: 0.9em;
        }

        .form-group input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
        }

        /* 按钮样式 */
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn.loading {
            position: relative;
            color: transparent;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            margin-left: -8px;
            margin-top: -8px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .btn-primary {
            background: #4299e1;
            color: white;
        }

        .btn-primary:hover {
            background: #3182ce;
        }

        .btn-secondary {
            background: #718096;
            color: white;
        }

        .btn-secondary:hover {
            background: #4a5568;
        }

        .btn-success {
            background: #48bb78;
            color: white;
        }

        .btn-success:hover {
            background: #38a169;
        }

        .btn-warning {
            background: #ed8936;
            color: white;
        }

        .btn-warning:hover {
            background: #dd6b20;
        }

        .btn-info {
            background: #38b2ac;
            color: white;
        }

        .btn-info:hover {
            background: #319795;
        }

        /* 结果显示区域 */
        .result-container {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 500px;
            overflow-y: auto;
        }

        .result-container:empty {
            display: none;
        }

        .result-container pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .result-container .success {
            color: #38a169;
            font-weight: 500;
        }

        .result-container .error {
            color: #e53e3e;
            font-weight: 500;
        }

        .result-container .info {
            color: #3182ce;
            font-weight: 500;
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 页面底部 */
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px 0;
            border-top: 1px solid #e2e8f0;
            color: #718096;
            font-size: 0.9em;
        }

        .footer a {
            color: #4299e1;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
                border-radius: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .nav-tabs {
                flex-direction: column;
            }
            
            .tab-btn {
                border-radius: 8px;
                margin-bottom: 5px;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 服务器配置区域 -->
        <div class="server-config">
            <h3>🔧 服务器配置</h3>
            <div class="form-group">
                <label for="server-url">服务器地址:</label>
                <input type="text" id="server-url" value="" placeholder="例如: http://localhost:50091">
                <small>请输入音乐解锁服务的完整地址</small>
            </div>
            <button class="btn" id="update-server-btn">更新配置</button>
            <button class="btn" id="test-connection-btn">测试连接</button>
        </div>

        <!-- 页面头部 -->
        <header class="header">
            <h1>🎵 音乐解锁服务 - 独立测试工具</h1>
            <p class="subtitle">基于UnblockNeteaseMusic的音乐解锁服务API测试工具 (独立版本)</p>
            <div class="status-bar">
                <span id="service-status" class="status-indicator initial">未连接</span>
                <span id="api-version" class="version-info">等待连接</span>
            </div>
        </header>

        <!-- 功能导航 -->
        <nav class="nav-tabs">
            <button class="tab-btn active" data-tab="song">万能API</button>
            <button class="tab-btn" data-tab="unlock">批量解锁</button>
            <button class="tab-btn" data-tab="sources">音源管理</button>
            <button class="tab-btn" data-tab="docs">API文档</button>
        </nav>

        <!-- 万能解锁API测试 -->
        <div id="song-tab" class="tab-content active">
            <div class="section">
                <h2>🎯 万能解锁API</h2>
                <p class="api-description">
                    <strong>新版API结构：</strong>使用统一的万能解锁API，通过不同参数实现多种功能模式
                </p>
                <div class="form-group">
                    <label for="song-id">歌曲ID:</label>
                    <input type="text" id="song-id" placeholder="例如: 418602084 (周杰伦-稻香)" value="">
                    <small>网易云音乐歌曲ID，可从歌曲链接中获取</small>
                </div>
                <div class="form-group">
                    <label for="song-sources">指定音源 (可选):</label>
                    <input type="text" id="song-sources" placeholder="例如: qq,kugou,kuwo">
                    <small>多个音源用逗号分隔，留空使用默认音源</small>
                </div>
                <div class="button-group">
                    <button id="get-song-info-btn" class="btn btn-primary">获取详细信息 (mode=detail)</button>
                    <button id="check-song-availability-btn" class="btn btn-info">检查状态 (mode=status)</button>
                </div>
                <div id="song-result" class="result-container"></div>
            </div>
        </div>

        <!-- 批量解锁测试 -->
        <div id="unlock-tab" class="tab-content">
            <div class="section">
                <h2>🔓 批量音乐解锁</h2>
                <p class="api-description">
                    <strong>批量解锁功能：</strong>使用万能API的unlock模式，支持多首歌曲同时解锁
                </p>
                <div class="form-group">
                    <label for="unlock-ids">歌曲ID列表:</label>
                    <textarea id="unlock-ids" placeholder="输入歌曲ID，每行一个或用逗号分隔&#10;例如:&#10;418602084&#10;186016&#10;185868" rows="4"></textarea>
                    <small>支持多个歌曲ID，每行一个或用逗号分隔</small>
                </div>
                <div class="form-group">
                    <label for="unlock-sources">指定音源 (可选):</label>
                    <input type="text" id="unlock-sources" placeholder="例如: qq,kugou,kuwo">
                </div>
                <div class="form-group">
                    <label>音质处理:</label>
                    <div class="info-text">
                        <span style="color: #28a745;">✓ UnblockNeteaseMusic自动管理</span>
                        <small style="display: block; color: #6c757d; margin-top: 4px;">
                            系统会自动选择最佳可用音质，无需手动配置
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="detailed-result"> 返回详细信息
                    </label>
                </div>
                <div class="button-group">
                    <button id="unlock-songs-btn" class="btn btn-primary">批量解锁 (mode=unlock)</button>
                    <button id="quick-unlock-btn" class="btn btn-success">快速解锁 (format=minimal)</button>
                    <button id="check-unlock-status-btn" class="btn btn-info">检查状态 (mode=status)</button>
                </div>
                <div id="unlock-result" class="result-container"></div>
            </div>
        </div>

        <!-- 音源管理测试 -->
        <div id="sources-tab" class="tab-content">
            <div class="section">
                <h2>🎯 音源管理</h2>
                <p class="api-description">
                    <strong>音源管理功能：</strong>使用简化的音源API和万能API的test模式
                </p>
                <div class="button-group">
                    <button id="get-sources-btn" class="btn btn-primary">获取音源列表 (/api/sources)</button>
                    <button id="get-sources-with-status-btn" class="btn btn-secondary">获取音源状态</button>
                    <button id="test-all-sources-btn" class="btn btn-warning">测试所有音源 (mode=test)</button>
                    <button id="get-source-stats-btn" class="btn btn-info">音源统计 (/api/sources/stats)</button>
                </div>
                <div class="form-group">
                    <label for="test-source-id">测试单个音源:</label>
                    <select id="test-source-id">
                        <option value="qq">QQ音乐</option>
                        <option value="kugou">酷狗音乐</option>
                        <option value="kuwo">酷我音乐</option>
                        <option value="migu">咪咕音乐</option>
                        <option value="joox">JOOX</option>
                        <option value="youtube">YouTube</option>
                    </select>
                    <button id="test-single-source-btn" class="btn btn-secondary">测试 (mode=test)</button>
                </div>
                <div id="sources-result" class="result-container"></div>
            </div>
        </div>

        <!-- API文档 -->
        <div id="docs-tab" class="tab-content">
            <div class="section">
                <h2>📚 API文档</h2>
                <p class="api-description">
                    <strong>新版API结构：</strong>从17个API优化为7个API，减少58.8%的冗余
                </p>
                <div class="button-group">
                    <button id="load-api-docs-btn" class="btn btn-primary">加载完整API文档</button>
                    <button id="open-api-docs-btn" class="btn btn-secondary">在新窗口打开</button>
                </div>
                <div id="docs-result" class="result-container"></div>
            </div>
        </div>

        <!-- 页面底部 -->
        <footer class="footer">
            <p>&copy; 2024 音乐解锁服务 | 基于 <a href="https://github.com/UnblockNeteaseMusic/server" target="_blank">UnblockNeteaseMusic</a></p>
            <p>仅供学习和研究使用，请支持正版音乐</p>
            <p><strong>独立测试工具版本</strong> - 可脱离项目运行</p>
        </footer>
    </div>

    <script>
        /**
         * 音乐解锁服务独立测试工具JavaScript代码
         * 实现所有的前端交互功能
         */

        // 全局配置 - 动态获取服务器地址 (硬编码优化)
        let SERVER_BASE = (() => {
            // 优先使用当前页面的origin
            if (window.location.origin && window.location.origin !== 'null') {
                return window.location.origin;
            }
            // 回退到localStorage保存的配置
            const saved = localStorage.getItem('music-unlock-server');
            if (saved) {
                return saved;
            }
            // 最后回退到默认值
            return 'http://localhost:50091';
        })();
        let API_BASE = SERVER_BASE + '/api';
        let HEALTH_CHECK_URL = SERVER_BASE + '/';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('音乐解锁服务独立测试工具已加载');

            try {
                // 从localStorage恢复服务器配置 (硬编码优化)
                const savedServer = localStorage.getItem('music-unlock-server');
                if (savedServer) {
                    document.getElementById('server-url').value = savedServer;
                    console.log('已恢复保存的服务器配置:', savedServer);
                } else {
                    // 使用动态获取的服务器地址作为默认值
                    document.getElementById('server-url').value = SERVER_BASE;
                }

                // 加载UI配置 (P2硬编码优化)
                await loadUIConfig();

                // 初始化页面
                await initializePage();

                // 绑定标签切换事件
                bindTabEvents();

                // 绑定所有按钮事件
                bindButtonEvents();

                // 显示页面（防闪烁）
                document.body.classList.add('loaded');

                // 延迟检查服务状态，避免初始闪烁
                setTimeout(() => {
                    checkServiceStatus();
                }, 100);

                // 定期检查服务状态
                setInterval(checkServiceStatus, 30000); // 每30秒检查一次

                console.log('页面初始化完成');
            } catch (error) {
                console.error('页面初始化失败:', error);
                document.body.classList.add('loaded'); // 即使出错也要显示页面
            }
        });

        /**
         * 更新服务器配置
         */
        function updateServerConfig() {
            const serverUrl = document.getElementById('server-url').value.trim();
            if (!serverUrl) {
                alert('请输入服务器地址');
                return;
            }

            // 移除末尾的斜杠
            SERVER_BASE = serverUrl.replace(/\/$/, '');
            API_BASE = SERVER_BASE + '/api';
            HEALTH_CHECK_URL = SERVER_BASE + '/';

            // 保存配置到localStorage (硬编码优化)
            localStorage.setItem('music-unlock-server', SERVER_BASE);

            console.log('服务器配置已更新并保存:', SERVER_BASE);

            // 立即检查服务状态
            checkServiceStatus();
        }

        /**
         * 测试连接
         */
        async function testConnection() {
            const statusElement = document.getElementById('service-status');
            const versionElement = document.getElementById('api-version');

            try {
                statusElement.textContent = '测试连接中...';
                statusElement.className = 'status-indicator checking';

                const response = await fetch(HEALTH_CHECK_URL);
                const data = await response.json();

                if (response.ok && data.数据 && data.数据.status === 'healthy') {
                    statusElement.textContent = '🟢 连接成功';
                    statusElement.className = 'status-indicator online';
                    versionElement.textContent = `v${data.数据.version}`;
                    alert('连接测试成功！');
                } else {
                    throw new Error('服务响应异常');
                }
            } catch (error) {
                statusElement.textContent = '🔴 连接失败';
                statusElement.className = 'status-indicator offline';
                versionElement.textContent = '连接失败';
                alert('连接测试失败: ' + error.message);
                console.error('连接测试失败:', error);
            }
        }

        /**
         * 初始化页面 (P2硬编码优化)
         */
        async function initializePage() {
            try {
                // 从配置API获取默认测试数据
                const configResponse = await fetch(`${API_BASE}/config`);
                if (configResponse.ok) {
                    const configData = await configResponse.json();
                    const testing = configData.testing;

                    // 设置默认值
                    document.getElementById('song-id').value = testing.defaultSongId;
                    document.getElementById('unlock-ids').value = testing.defaultSongIds.join('\n');

                    console.log('已从配置API加载默认测试数据:', testing);
                } else {
                    // 配置API失败时使用硬编码默认值
                    console.warn('配置API获取失败，使用默认值');
                    setDefaultTestData();
                }
            } catch (error) {
                console.warn('配置API请求失败，使用默认值:', error);
                setDefaultTestData();
            }
        }

        /**
         * 设置默认测试数据 (配置API失败时的回退方案)
         */
        function setDefaultTestData() {
            document.getElementById('song-id').value = '418602084';
            document.getElementById('unlock-ids').value = '418602084\n186016\n185868';
        }

        /**
         * 加载UI配置 (P2硬编码优化)
         */
        async function loadUIConfig() {
            try {
                // 先检查localStorage缓存
                const cachedConfig = localStorage.getItem('music-unlock-ui-config');
                const cacheTime = localStorage.getItem('music-unlock-ui-config-time');
                const now = Date.now();

                // 如果缓存存在且未过期（1小时），使用缓存
                if (cachedConfig && cacheTime && (now - parseInt(cacheTime)) < 3600000) {
                    const uiConfig = JSON.parse(cachedConfig);
                    applyUIConfig(uiConfig);
                    console.log('已从缓存加载UI配置');
                    return;
                }

                // 从配置API获取UI配置
                const configResponse = await fetch(`${API_BASE}/config`);
                if (configResponse.ok) {
                    const configData = await configResponse.json();
                    const uiConfig = configData.ui;

                    // 应用UI配置
                    applyUIConfig(uiConfig);

                    // 缓存配置
                    localStorage.setItem('music-unlock-ui-config', JSON.stringify(uiConfig));
                    localStorage.setItem('music-unlock-ui-config-time', now.toString());

                    console.log('已从配置API加载UI配置:', uiConfig);
                } else {
                    console.warn('UI配置API获取失败，使用默认样式');
                }
            } catch (error) {
                console.warn('UI配置加载失败，使用默认样式:', error);
            }
        }

        /**
         * 应用UI配置到CSS变量
         */
        function applyUIConfig(uiConfig) {
            if (!uiConfig) return;

            const root = document.documentElement;

            // 应用主题颜色
            if (uiConfig.theme) {
                if (uiConfig.theme.primaryColor) {
                    root.style.setProperty('--primary-color', uiConfig.theme.primaryColor);
                }
                if (uiConfig.theme.secondaryColor) {
                    root.style.setProperty('--secondary-color', uiConfig.theme.secondaryColor);
                }
                if (uiConfig.theme.accentColor) {
                    root.style.setProperty('--accent-color', uiConfig.theme.accentColor);
                }
                if (uiConfig.theme.successColor) {
                    root.style.setProperty('--success-color', uiConfig.theme.successColor);
                }
                if (uiConfig.theme.errorColor) {
                    root.style.setProperty('--error-color', uiConfig.theme.errorColor);
                }
                if (uiConfig.theme.warningColor) {
                    root.style.setProperty('--warning-color', uiConfig.theme.warningColor);
                }
            }

            // 应用布局配置
            if (uiConfig.layout) {
                if (uiConfig.layout.maxWidth) {
                    root.style.setProperty('--max-width', uiConfig.layout.maxWidth);
                }
                if (uiConfig.layout.headerFontSize) {
                    root.style.setProperty('--header-font-size', uiConfig.layout.headerFontSize);
                }
                if (uiConfig.layout.baseFontSize) {
                    root.style.setProperty('--base-font-size', uiConfig.layout.baseFontSize);
                }
            }
        }

        // 防抖变量
        let statusCheckTimeout = null;

        /**
         * 检查服务状态（带防抖）
         */
        async function checkServiceStatus() {
            // 清除之前的检查
            if (statusCheckTimeout) {
                clearTimeout(statusCheckTimeout);
            }

            const statusElement = document.getElementById('service-status');
            const versionElement = document.getElementById('api-version');

            if (!statusElement || !versionElement) {
                console.warn('状态元素未找到');
                return;
            }

            try {
                // 只在初始状态时显示"检查中"，避免频繁闪烁
                if (statusElement.classList.contains('initial')) {
                    statusElement.textContent = '检查中...';
                    statusElement.className = 'status-indicator checking';
                }

                // 检查健康状态
                const healthResponse = await fetch(HEALTH_CHECK_URL, {
                    method: 'GET',
                    cache: 'no-cache',
                    timeout: 5000
                });
                const healthData = await healthResponse.json();

                if (healthResponse.ok && healthData.数据 && healthData.数据.status === 'healthy') {
                    statusElement.textContent = '🟢 服务正常';
                    statusElement.className = 'status-indicator online';
                    versionElement.textContent = `v${healthData.数据.version}`;
                } else {
                    throw new Error('服务响应异常');
                }
            } catch (error) {
                statusElement.textContent = '🔴 服务异常';
                statusElement.className = 'status-indicator offline';
                versionElement.textContent = '无法连接';

                // 只在开发模式下输出详细错误
                if (error.name !== 'TypeError') {
                    console.error('服务状态检查失败:', error.message);
                }
            }
        }

        /**
         * 绑定标签切换事件
         */
        function bindTabEvents() {
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // 移除所有活动状态
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // 激活当前标签
                    this.classList.add('active');
                    document.getElementById(targetTab + '-tab').classList.add('active');
                });
            });
        }

        /**
         * 绑定所有按钮事件
         */
        function bindButtonEvents() {
            console.log('开始绑定按钮事件...');

            try {
                // 服务器配置相关按钮
                bindButtonSafely('update-server-btn', updateServerConfig);
                bindButtonSafely('test-connection-btn', testConnection);

                // 歌曲信息相关按钮
                bindButtonSafely('get-song-info-btn', getSongInfo);
                bindButtonSafely('check-song-availability-btn', checkSongAvailability);

                // 批量解锁相关按钮
                bindButtonSafely('unlock-songs-btn', unlockSongs);
                bindButtonSafely('quick-unlock-btn', quickUnlock);
                bindButtonSafely('check-unlock-status-btn', checkUnlockStatus);

                // 音源管理相关按钮
                bindButtonSafely('get-sources-btn', getSources);
                bindButtonSafely('get-sources-with-status-btn', getSourcesWithStatus);
                bindButtonSafely('test-all-sources-btn', testAllSources);
                bindButtonSafely('get-source-stats-btn', getSourceStats);
                bindButtonSafely('test-single-source-btn', testSingleSource);

                // API文档相关按钮
                bindButtonSafely('load-api-docs-btn', loadApiDocs);
                bindButtonSafely('open-api-docs-btn', openApiDocs);

                console.log('按钮事件绑定完成');
            } catch (error) {
                console.error('按钮事件绑定失败:', error);
            }
        }

        /**
         * 安全地绑定按钮事件
         */
        function bindButtonSafely(buttonId, handler) {
            const button = document.getElementById(buttonId);
            if (button) {
                button.addEventListener('click', function(event) {
                    // 添加点击反馈
                    showButtonFeedback(button);
                    // 调用原始处理函数
                    handler(event);
                });
                console.log(`✓ 绑定按钮: ${buttonId}`);
            } else {
                console.warn(`⚠ 按钮未找到: ${buttonId}`);
            }
        }

        /**
         * 显示按钮点击反馈
         */
        function showButtonFeedback(button) {
            // 添加点击效果
            button.style.transform = 'scale(0.95)';
            setTimeout(() => {
                button.style.transform = '';
            }, 150);
        }

        /**
         * 设置按钮加载状态
         */
        function setButtonLoading(buttonId, loading = true) {
            const button = document.getElementById(buttonId);
            if (button) {
                if (loading) {
                    button.classList.add('loading');
                    button.disabled = true;
                } else {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
            }
        }

        /**
         * 显示结果
         */
        function displayResult(containerId, data, title = '') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleString();

            let content = '';
            if (title) {
                content += `<div class="info"><strong>${title}</strong> (${timestamp})</div>`;
            }

            if (typeof data === 'object') {
                content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } else {
                content += `<div>${data}</div>`;
            }

            container.innerHTML = content;
        }

        /**
         * 显示错误
         */
        function displayError(containerId, error, title = '请求失败') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleString();

            // 智能错误信息格式化
            let errorMessage = '';
            if (error && typeof error === 'object') {
                if (error.message) {
                    errorMessage = error.message;
                } else if (error.消息) {
                    errorMessage = error.消息;
                } else if (error.error) {
                    errorMessage = error.error;
                } else {
                    // 尝试JSON序列化显示对象内容
                    try {
                        errorMessage = JSON.stringify(error, null, 2);
                    } catch (e) {
                        errorMessage = String(error);
                    }
                }
            } else {
                errorMessage = String(error || '未知错误');
            }

            container.innerHTML = `
                <div class="error"><strong>${title}</strong> (${timestamp})</div>
                <div>错误信息: ${errorMessage}</div>
            `;
        }

        /**
         * 显示加载状态
         */
        function showLoading(containerId, message = '请求中...') {
            const container = document.getElementById(containerId);
            container.innerHTML = `
                <div class="info">
                    <span class="loading"></span>${message}
                </div>
            `;
        }

        /**
         * 获取歌曲详细信息
         */
        async function getSongInfo() {
            const songId = document.getElementById('song-id').value.trim();
            const sources = document.getElementById('song-sources').value.trim();

            if (!songId) {
                alert('请输入歌曲ID');
                return;
            }

            showLoading('song-result', '获取歌曲详细信息中...');

            try {
                let url = `${API_BASE}/unlock?songIds=${songId}&mode=detail&format=full`;
                if (sources) {
                    url += `&sources=${sources}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (response.ok) {
                    displayResult('song-result', data, '歌曲详细信息');
                } else {
                    displayError('song-result', data, '获取歌曲信息失败');
                }
            } catch (error) {
                displayError('song-result', error, '网络请求失败');
            }
        }

        /**
         * 检查歌曲可用性
         */
        async function checkSongAvailability() {
            const songId = document.getElementById('song-id').value.trim();
            const sources = document.getElementById('song-sources').value.trim();

            if (!songId) {
                alert('请输入歌曲ID');
                return;
            }

            showLoading('song-result', '检查歌曲状态中...');

            try {
                let url = `${API_BASE}/unlock?songIds=${songId}&mode=status&format=full`;
                if (sources) {
                    url += `&sources=${sources}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (response.ok) {
                    displayResult('song-result', data, '歌曲状态检查');
                } else {
                    displayError('song-result', data, '检查歌曲状态失败');
                }
            } catch (error) {
                displayError('song-result', error, '网络请求失败');
            }
        }

        /**
         * 批量解锁歌曲
         */
        async function unlockSongs() {
            const idsText = document.getElementById('unlock-ids').value.trim();
            const sources = document.getElementById('unlock-sources').value.trim();
            // minBitrate参数已移除 - 音质由UnblockNeteaseMusic自动管理
            const detailed = document.getElementById('detailed-result').checked;

            if (!idsText) {
                alert('请输入歌曲ID列表');
                return;
            }

            // 处理歌曲ID列表
            const songIds = idsText.split(/[,\n]/).map(id => id.trim()).filter(id => id);

            showLoading('unlock-result', '批量解锁中...');

            try {
                let url = `${API_BASE}/unlock?songIds=${songIds.join(',')}&mode=unlock&format=${detailed ? 'full' : 'minimal'}`;
                if (sources) {
                    url += `&sources=${sources}`;
                }
                // minBitrate参数已移除 - 音质由UnblockNeteaseMusic自动管理

                const response = await fetch(url);
                const data = await response.json();

                if (response.ok) {
                    displayResult('unlock-result', data, '批量解锁结果');
                } else {
                    displayError('unlock-result', data, '批量解锁失败');
                }
            } catch (error) {
                displayError('unlock-result', error, '网络请求失败');
            }
        }

        /**
         * 快速解锁
         */
        async function quickUnlock() {
            const idsText = document.getElementById('unlock-ids').value.trim();
            const sources = document.getElementById('unlock-sources').value.trim();

            if (!idsText) {
                alert('请输入歌曲ID列表');
                return;
            }

            const songIds = idsText.split(/[,\n]/).map(id => id.trim()).filter(id => id);

            showLoading('unlock-result', '快速解锁中...');

            try {
                let url = `${API_BASE}/unlock?songIds=${songIds.join(',')}&mode=unlock&format=minimal`;
                if (sources) {
                    url += `&sources=${sources}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (response.ok) {
                    displayResult('unlock-result', data, '快速解锁结果');
                } else {
                    displayError('unlock-result', data, '快速解锁失败');
                }
            } catch (error) {
                displayError('unlock-result', error, '网络请求失败');
            }
        }

        /**
         * 检查解锁状态
         */
        async function checkUnlockStatus() {
            const idsText = document.getElementById('unlock-ids').value.trim();

            if (!idsText) {
                alert('请输入歌曲ID列表');
                return;
            }

            const songIds = idsText.split(/[,\n]/).map(id => id.trim()).filter(id => id);

            showLoading('unlock-result', '检查解锁状态中...');

            try {
                const url = `${API_BASE}/unlock?songIds=${songIds.join(',')}&mode=status&format=full`;
                const response = await fetch(url);
                const data = await response.json();

                if (response.ok) {
                    displayResult('unlock-result', data, '解锁状态检查');
                } else {
                    displayError('unlock-result', data, '检查解锁状态失败');
                }
            } catch (error) {
                displayError('unlock-result', error, '网络请求失败');
            }
        }

        /**
         * 获取音源列表
         */
        async function getSources() {
            showLoading('sources-result', '获取音源列表中...');

            try {
                const response = await fetch(`${API_BASE}/sources`);
                const data = await response.json();

                if (response.ok) {
                    displayResult('sources-result', data, '音源列表');
                } else {
                    displayError('sources-result', data, '获取音源列表失败');
                }
            } catch (error) {
                displayError('sources-result', error, '网络请求失败');
            }
        }

        /**
         * 获取音源状态
         */
        async function getSourcesWithStatus() {
            showLoading('sources-result', '获取音源状态中...');

            try {
                const response = await fetch(`${API_BASE}/sources?includeStatus=true`);
                const data = await response.json();

                if (response.ok) {
                    displayResult('sources-result', data, '音源状态');
                } else {
                    displayError('sources-result', data, '获取音源状态失败');
                }
            } catch (error) {
                displayError('sources-result', error, '网络请求失败');
            }
        }

        /**
         * 测试所有音源
         */
        async function testAllSources() {
            showLoading('sources-result', '测试所有音源中...');

            try {
                // 测试模式需要指定音源列表，这里测试所有主要音源
                const allSources = 'migu,kuwo,qq,kugou,joox,youtube';
                const response = await fetch(`${API_BASE}/unlock?mode=test&sources=${allSources}&format=full`);
                const data = await response.json();

                if (response.ok) {
                    displayResult('sources-result', data, '音源测试结果');
                } else {
                    displayError('sources-result', data, '测试音源失败');
                }
            } catch (error) {
                displayError('sources-result', error, '网络请求失败');
            }
        }

        /**
         * 获取音源统计
         */
        async function getSourceStats() {
            showLoading('sources-result', '获取音源统计中...');

            try {
                const response = await fetch(`${API_BASE}/sources/stats`);
                const data = await response.json();

                if (response.ok) {
                    displayResult('sources-result', data, '音源统计');
                } else {
                    displayError('sources-result', data, '获取音源统计失败');
                }
            } catch (error) {
                displayError('sources-result', error, '网络请求失败');
            }
        }

        /**
         * 测试单个音源
         */
        async function testSingleSource() {
            const sourceId = document.getElementById('test-source-id').value;

            showLoading('sources-result', `测试${sourceId}音源中...`);

            try {
                const response = await fetch(`${API_BASE}/unlock?mode=test&sources=${sourceId}&format=full`);
                const data = await response.json();

                if (response.ok) {
                    displayResult('sources-result', data, `${sourceId}音源测试结果`);
                } else {
                    displayError('sources-result', data, `测试${sourceId}音源失败`);
                }
            } catch (error) {
                displayError('sources-result', error, '网络请求失败');
            }
        }

        /**
         * 加载API文档
         */
        async function loadApiDocs() {
            showLoading('docs-result', '加载API文档中...');

            try {
                const response = await fetch(`${API_BASE}/docs`);
                const data = await response.json();

                if (response.ok) {
                    displayResult('docs-result', data, 'API文档');
                } else {
                    displayError('docs-result', data, '加载API文档失败');
                }
            } catch (error) {
                displayError('docs-result', error, '网络请求失败');
            }
        }

        /**
         * 在新窗口打开API文档
         */
        function openApiDocs() {
            const url = `${API_BASE}/docs`;
            window.open(url, '_blank');
        }
    </script>
</body>
</html>
