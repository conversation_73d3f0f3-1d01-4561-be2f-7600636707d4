/**
 * 应用配置管理模块
 * 负责加载和管理所有环境变量和应用配置
 */

require('dotenv').config();

const config = {
    // 服务器基础配置
    server: {
        port: process.env.PORT || 3000,
        host: process.env.HOST || 'localhost',
        env: process.env.NODE_ENV || 'development'
    },

    // 网络超时配置 (硬编码优化)
    timeout: {
        unlock: parseInt(process.env.UNLOCK_TIMEOUT) || 30000,
        sourceTest: parseInt(process.env.SOURCE_TEST_TIMEOUT) || 10000,
        apiRequest: parseInt(process.env.API_REQUEST_TIMEOUT) || 30000,
        healthCheck: parseInt(process.env.HEALTH_CHECK_TIMEOUT) || 5000
    },

    // 性能配置 (硬编码优化)
    performance: {
        batchConcurrency: parseInt(process.env.BATCH_CONCURRENCY) || 5,
        maxRetries: parseInt(process.env.MAX_RETRIES) || 3,
        retryDelay: parseInt(process.env.RETRY_DELAY) || 1000
    },

    // API限制配置 (P1硬编码优化)
    limits: {
        maxBatchSize: parseInt(process.env.MAX_BATCH_SIZE) || 20,
        maxSearchResults: parseInt(process.env.MAX_SEARCH_RESULTS) || 50,
        maxKeywordLength: parseInt(process.env.MAX_KEYWORD_LENGTH) || 100,
        requestTimeout: parseInt(process.env.REQUEST_TIMEOUT) || 30000
    },

    // 缓存配置 (P1硬编码优化)
    cache: {
        metadataTTL: parseInt(process.env.CACHE_METADATA_TTL) || 3600,
        searchTTL: parseInt(process.env.CACHE_SEARCH_TTL) || 1800,
        unlockTTL: parseInt(process.env.CACHE_UNLOCK_TTL) || 7200,
        enabled: process.env.CACHE_ENABLED !== 'false'
    },

    // 日志配置 (P2硬编码优化)
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        fileEnabled: process.env.LOG_FILE_ENABLED === 'true',
        consoleEnabled: process.env.LOG_CONSOLE_ENABLED !== 'false',
        maxFiles: process.env.LOG_MAX_FILES || '14d',
        maxSize: process.env.LOG_MAX_SIZE || '20m',
        datePattern: process.env.LOG_DATE_PATTERN || 'YYYY-MM-DD',
        timeFormat: process.env.LOG_TIME_FORMAT || 'YYYY-MM-DD HH:mm:ss'
    },

    // 测试数据配置 (P2硬编码优化)
    testing: {
        defaultSongId: process.env.TEST_SONG_ID || '418602084',
        defaultSongIds: process.env.TEST_SONG_IDS?.split(',') || ['418602084', '186016', '185868'],
        testKeywords: process.env.TEST_KEYWORDS?.split(',') || ['周杰伦', '稻香', '青花瓷'],
        testSources: process.env.TEST_SOURCES || 'migu,kuwo,qq,kugou,joox,youtube',
        testArtists: process.env.TEST_ARTISTS?.split(',') || ['周杰伦', '林俊杰', '邓紫棋'],
        testAlbums: process.env.TEST_ALBUMS?.split(',') || ['叶惠美', '魔杰座', '十二新作']
    },

    // UI配置 (P2硬编码优化)
    ui: {
        theme: {
            primaryColor: process.env.UI_PRIMARY_COLOR || '#4299e1',
            secondaryColor: process.env.UI_SECONDARY_COLOR || '#718096',
            accentColor: process.env.UI_ACCENT_COLOR || '#2196f3',
            successColor: process.env.UI_SUCCESS_COLOR || '#48bb78',
            errorColor: process.env.UI_ERROR_COLOR || '#f56565',
            warningColor: process.env.UI_WARNING_COLOR || '#ed8936'
        },
        layout: {
            maxWidth: process.env.UI_MAX_WIDTH || '1200px',
            headerFontSize: process.env.UI_HEADER_FONT_SIZE || '2.5em',
            baseFontSize: process.env.UI_BASE_FONT_SIZE || '1em'
        }
    },

    // UnblockNeteaseMusic 配置
    music: {
    // 音源优先级配置
        sources: process.env.MUSIC_SOURCES ? 
            process.env.MUSIC_SOURCES.split(',').map(s => s.trim()) : 
            ['qq', 'kugou', 'kuwo', 'migu'],
    
        // 网易云音乐配置
        neteaseCookie: process.env.NETEASE_COOKIE || '',
        enableFlac: process.env.ENABLE_FLAC === 'true',
        enableLocalVip: process.env.ENABLE_LOCAL_VIP || false,
        // minBitrate配置已移除 - 音质由UnblockNeteaseMusic自动处理
    
        // 第三方音源配置
        qqCookie: process.env.QQ_COOKIE || '',
        miguCookie: process.env.MIGU_COOKIE || '',
        jooxCookie: process.env.JOOX_COOKIE || '',
        youtubeKey: process.env.YOUTUBE_KEY || '',
    
        // 功能开关
        followSourceOrder: process.env.FOLLOW_SOURCE_ORDER === 'true',
        blockAds: process.env.BLOCK_ADS === 'true'
    },

    // 安全配置
    security: {
        // 生产环境应设置具体的域名，开发环境可使用*
        corsOrigin: process.env.CORS_ORIGIN || (process.env.NODE_ENV === 'production' ? 'https://yourdomain.com' : '*'),
        rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15分钟
        rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
        maxRequestSize: process.env.MAX_REQUEST_SIZE || '10mb',
        enableHttpsRedirect: process.env.ENABLE_HTTPS_REDIRECT === 'true',
        sessionSecret: process.env.SESSION_SECRET || 'change-this-in-production'
    },

    // API配置
    api: {
        prefix: '/api',
        version: 'v1'
        // timeout 和 maxSearchResults 已迁移到 limits 配置段
    }
};

/**
 * 验证必要的配置项
 */
function validateConfig() {
    const errors = [];

    // 验证端口号
    if (isNaN(config.server.port) || config.server.port < 1 || config.server.port > 65535) {
        errors.push('无效的端口号配置');
    }

    // 验证超时配置 (硬编码优化)
    if (config.timeout.unlock < 5000 || config.timeout.unlock > 120000) {
        errors.push('解锁超时配置应在5-120秒之间');
    }
    if (config.timeout.sourceTest < 3000 || config.timeout.sourceTest > 60000) {
        errors.push('音源测试超时配置应在3-60秒之间');
    }

    // 验证性能配置 (硬编码优化)
    if (config.performance.batchConcurrency < 1 || config.performance.batchConcurrency > 20) {
        errors.push('批量并发数应在1-20之间');
    }

    // 验证安全配置
    if (process.env.NODE_ENV === 'production') {
        if (config.security.corsOrigin === '*') {
            errors.push('生产环境不应使用通配符CORS配置，请设置具体域名');
        }
        if (config.security.sessionSecret === 'change-this-in-production') {
            errors.push('生产环境必须设置安全的SESSION_SECRET');
        }
    }

    // 验证音源配置
    const validSources = ['qq', 'kugou', 'kuwo', 'migu', 'joox', 'youtube', 'ytdlp', 'bilibili'];
    const invalidSources = config.music.sources.filter(source => !validSources.includes(source));
    if (invalidSources.length > 0) {
        errors.push(`无效的音源配置: ${invalidSources.join(', ')}`);
    }

    // 验证安全配置
    if (config.server.env === 'production') {
        if (config.security.corsOrigin === '*') {
            errors.push('生产环境不应使用通配符CORS配置');
        }
        if (config.security.sessionSecret === 'change-this-in-production') {
            errors.push('生产环境必须设置安全的SESSION_SECRET');
        }
    }

    if (errors.length > 0) {
        throw new Error(`配置验证失败:\n${errors.join('\n')}`);
    }
}

// 验证配置
validateConfig();

module.exports = config;
