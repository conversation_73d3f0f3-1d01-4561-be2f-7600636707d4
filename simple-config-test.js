/**
 * 简单配置效果测试
 * 直接测试当前配置下的解锁效果
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:50091';
const TEST_SONG_ID = '418602084'; // 周杰伦 - 稻香

async function testCurrentConfig() {
    console.log('🧪 测试当前配置效果\n');
    
    try {
        // 1. 获取当前配置状态
        console.log('📋 当前配置状态:');
        const configResponse = await axios.get(`${BASE_URL}/music/source`);
        const config = configResponse.data;
        
        console.log(`   启用本地VIP: ${config.启用本地VIP}`);
        console.log(`   遵循音源顺序: ${config.遵循音源顺序}`);
        console.log(`   启用无损: ${config.启用无损}`);
        console.log(`   音源总数: ${config.音源总数}`);
        console.log(`   已启用音源: ${config.已启用音源}`);
        
        const topSources = config.音源优先级顺序.slice(0, 3);
        console.log(`   前3个音源: ${topSources.map(s => `${s.音源ID}(${s.优先级})`).join(', ')}`);
        
        // 2. 测试解锁效果
        console.log('\n🎵 解锁测试:');
        
        // 测试1: 使用前3个音源
        const sources1 = topSources.map(s => s.音源ID).join(',');
        console.log(`\n   测试1 - 多音源 (${sources1}):`);
        
        const test1Response = await axios.get(`${BASE_URL}/music/unlock`, {
            params: { sources: sources1, songs: TEST_SONG_ID },
            timeout: 30000
        });
        
        if (test1Response.data.成功列表) {
            const result = test1Response.data.成功列表;
            console.log(`     ✅ 成功 - 音源: ${result.音源ID}, 音质: ${result.音质}bps (${result.音质描述})`);
            console.log(`     📊 文件大小: ${result.文件大小}字节, 格式: ${result.格式}`);
            console.log(`     🔗 链接: ${result.播放链接 ? '有效' : '无效'}`);
        } else {
            console.log(`     ❌ 失败: ${test1Response.data.消息}`);
        }
        
        // 测试2: 只使用第一个音源
        const firstSource = topSources[0].音源ID;
        console.log(`\n   测试2 - 单音源 (${firstSource}):`);
        
        const test2Response = await axios.get(`${BASE_URL}/music/unlock`, {
            params: { sources: firstSource, songs: TEST_SONG_ID },
            timeout: 30000
        });
        
        if (test2Response.data.成功列表) {
            const result = test2Response.data.成功列表;
            console.log(`     ✅ 成功 - 音源: ${result.音源ID}, 音质: ${result.音质}bps (${result.音质描述})`);
            console.log(`     📊 文件大小: ${result.文件大小}字节, 格式: ${result.格式}`);
        } else {
            console.log(`     ❌ 失败: ${test2Response.data.消息}`);
        }
        
        // 测试3: 使用不同音源顺序
        const reversedSources = topSources.slice().reverse().map(s => s.音源ID).join(',');
        console.log(`\n   测试3 - 反向音源顺序 (${reversedSources}):`);
        
        const test3Response = await axios.get(`${BASE_URL}/music/unlock`, {
            params: { sources: reversedSources, songs: TEST_SONG_ID },
            timeout: 30000
        });
        
        if (test3Response.data.成功列表) {
            const result = test3Response.data.成功列表;
            console.log(`     ✅ 成功 - 音源: ${result.音源ID}, 音质: ${result.音质}bps (${result.音质描述})`);
            console.log(`     📊 文件大小: ${result.文件大小}字节, 格式: ${result.格式}`);
        } else {
            console.log(`     ❌ 失败: ${test3Response.data.消息}`);
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('   响应状态:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
testCurrentConfig();
