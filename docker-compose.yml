# 🐳 音乐解锁服务 - Docker Compose配置
# 完整的容器化部署方案，包含服务、缓存、监控

version: '3.8'

services:
  # 🎵 音乐解锁服务
  music-unlock-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: music-unlock-server
    restart: unless-stopped
    ports:
      - "${PORT:-3000}:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=3000
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - REDIS_URL=redis://redis:6379
      - NETEASE_COOKIE=${NETEASE_COOKIE:-}
      - QQ_COOKIE=${QQ_COOKIE:-}
      - MIGU_COOKIE=${MIGU_COOKIE:-}
      - JOOX_COOKIE=${JOOX_COOKIE:-}
      - YOUTUBE_KEY=${YOUTUBE_KEY:-}
      - PROXY_URL=${PROXY_URL:-}
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config:ro
    depends_on:
      - redis
    networks:
      - music-network

    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.music-unlock.rule=Host(`music.localhost`)"
      - "traefik.http.services.music-unlock.loadbalancer.server.port=3000"

  # 🗄️ Redis缓存
  redis:
    image: redis:7-alpine
    container_name: music-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - music-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    sysctls:
      - net.core.somaxconn=1024

  # 📊 监控 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: music-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - music-network

  # 📈 可视化 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: music-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    networks:
      - music-network
    depends_on:
      - prometheus

  # 🔍 日志收集 - Loki
  loki:
    image: grafana/loki:latest
    container_name: music-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki.yml:/etc/loki/local-config.yaml:ro
      - loki-data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - music-network

  # 📋 日志代理 - Promtail
  promtail:
    image: grafana/promtail:latest
    container_name: music-promtail
    restart: unless-stopped
    volumes:
      - ./logs:/var/log/app:ro
      - ./monitoring/promtail.yml:/etc/promtail/config.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock
    command: -config.file=/etc/promtail/config.yml
    networks:
      - music-network
    depends_on:
      - loki

  # 🌐 反向代理 - Nginx
  nginx:
    image: nginx:alpine
    container_name: music-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./public:/usr/share/nginx/html:ro
    depends_on:
      - music-unlock-server
    networks:
      - music-network


# 🌐 网络配置
networks:
  music-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 💾 数据卷
volumes:
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  loki-data:
    driver: local

# 🏷️ 标签
x-common-labels: &common-labels
  project: "music-unlock-server"
  version: "1.0.0"
  environment: "${NODE_ENV:-production}"
