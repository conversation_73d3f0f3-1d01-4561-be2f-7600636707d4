# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log

# 运行时文件
pids
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
.nyc_output

# 依赖目录
.npm
.eslintcache

# 可选的npm缓存目录
.npm

# 可选的REPL历史
.node_repl_history

# 输出的编译目录
dist/
build/

# 运行时数据
.cache/

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
tmp/
temp/
