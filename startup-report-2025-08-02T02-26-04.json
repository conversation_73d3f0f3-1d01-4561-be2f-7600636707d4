{"title": "🎵 音乐解锁服务快速启动报告", "timestamp": "2025-08-02T02:26:04.634Z", "duration": "2秒", "success": true, "summary": {"totalSteps": 10, "successSteps": 10, "failedSteps": 0, "errors": 0, "warnings": 0}, "checkResults": {"environment": true, "dependencies": true, "configuration": true, "service": true, "api": true}, "steps": [{"step": "Node.js版本检查", "status": "success", "details": "v24.3.0", "timestamp": "2025-08-02T02:26:02.428Z"}, {"step": "npm版本检查", "status": "success", "details": "11.4.2", "timestamp": "2025-08-02T02:26:02.659Z"}, {"step": "package.json检查", "status": "success", "details": "", "timestamp": "2025-08-02T02:26:02.660Z"}, {"step": "依赖检查", "status": "success", "details": "", "timestamp": "2025-08-02T02:26:02.660Z"}, {"step": ".env文件检查", "status": "success", "details": "", "timestamp": "2025-08-02T02:26:02.660Z"}, {"step": "配置验证", "status": "success", "details": "", "timestamp": "2025-08-02T02:26:02.725Z"}, {"step": "服务启动", "status": "success", "details": "端口: 50091", "timestamp": "2025-08-02T02:26:03.772Z"}, {"step": "服务首页测试", "status": "success", "details": "", "timestamp": "2025-08-02T02:26:03.775Z"}, {"step": "音源管理API测试", "status": "success", "details": "", "timestamp": "2025-08-02T02:26:03.778Z"}, {"step": "音乐解锁API测试", "status": "success", "details": "", "timestamp": "2025-08-02T02:26:04.634Z"}], "errors": [], "warnings": []}