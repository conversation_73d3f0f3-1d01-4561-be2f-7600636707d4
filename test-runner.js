#!/usr/bin/env node

/**
 * 🧪 音乐解锁服务测试运行器
 * 自动化测试脚本，包含配置验证、服务启动、API测试等
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const http = require('http');

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

// 日志函数
function log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const levelColors = {
        INFO: colors.blue,
        SUCCESS: colors.green,
        WARNING: colors.yellow,
        ERROR: colors.red,
        HEADER: colors.magenta
    };
    
    const color = levelColors[level] || colors.reset;
    console.log(`${color}[${level}]${colors.reset} ${message}`);
    if (data) {
        console.log(`${colors.cyan}${JSON.stringify(data, null, 2)}${colors.reset}`);
    }
}

// 测试配置
const TEST_CONFIG = {
    SERVER_URL: 'http://localhost:3000',
    TIMEOUT: 30000,
    RETRY_COUNT: 3,
    TEST_SONG_ID: '12345',
    TEST_KEYWORD: '周杰伦'
};

class TestRunner {
    constructor() {
        this.serverProcess = null;
        this.testResults = {
            configValidation: false,
            serverStart: false,
            apiTests: [],
            performance: {},
            totalTests: 0,
            passedTests: 0,
            failedTests: 0
        };
    }

    // 运行所有测试
    async runAllTests() {
        log('HEADER', '🧪 开始运行音乐解锁服务测试套件');
        
        try {
            // 1. 配置验证测试
            await this.testConfigValidation();
            
            // 2. 服务启动测试
            await this.testServerStart();
            
            // 3. API功能测试
            await this.testAPIFunctions();
            
            // 4. 性能测试
            await this.testPerformance();
            
            // 5. 生成测试报告
            await this.generateTestReport();
            
        } catch (error) {
            log('ERROR', '测试运行失败', { error: error.message });
        } finally {
            // 清理资源
            await this.cleanup();
        }
    }

    // 配置验证测试
    async testConfigValidation() {
        log('INFO', '📋 开始配置验证测试...');
        
        return new Promise((resolve, reject) => {
            exec('node scripts/validate-config.js', (error, stdout, stderr) => {
                if (error) {
                    log('ERROR', '配置验证失败', { error: error.message });
                    this.testResults.configValidation = false;
                    this.testResults.failedTests++;
                } else {
                    log('SUCCESS', '配置验证通过');
                    this.testResults.configValidation = true;
                    this.testResults.passedTests++;
                }
                this.testResults.totalTests++;
                resolve();
            });
        });
    }

    // 服务启动测试
    async testServerStart() {
        log('INFO', '🚀 开始服务启动测试...');
        
        return new Promise((resolve) => {
            // 启动服务
            this.serverProcess = spawn('npm', ['run', 'dev'], {
                stdio: ['pipe', 'pipe', 'pipe'],
                shell: true
            });

            let startupTimeout = setTimeout(() => {
                log('ERROR', '服务启动超时');
                this.testResults.serverStart = false;
                this.testResults.failedTests++;
                this.testResults.totalTests++;
                resolve();
            }, 15000);

            this.serverProcess.stdout.on('data', (data) => {
                const output = data.toString();
                if (output.includes('音乐解锁服务启动成功')) {
                    clearTimeout(startupTimeout);
                    log('SUCCESS', '服务启动成功');
                    this.testResults.serverStart = true;
                    this.testResults.passedTests++;
                    this.testResults.totalTests++;
                    
                    // 等待服务完全就绪
                    setTimeout(resolve, 2000);
                }
            });

            this.serverProcess.stderr.on('data', (data) => {
                const error = data.toString();
                if (error.includes('Error') || error.includes('error')) {
                    log('ERROR', '服务启动错误', { error });
                }
            });
        });
    }

    // API功能测试
    async testAPIFunctions() {
        log('INFO', '🔌 开始API功能测试...');
        
        const apiTests = [
            { name: '健康检查', path: '/music/health', method: 'GET' },
            { name: '音源状态', path: '/music/sources/status', method: 'GET' },
            { name: '搜索功能', path: `/music/search?keyword=${encodeURIComponent(TEST_CONFIG.TEST_KEYWORD)}&limit=5`, method: 'GET' },
            { name: '单首解锁', path: `/music/unlock/${TEST_CONFIG.TEST_SONG_ID}`, method: 'GET' }
        ];

        for (const test of apiTests) {
            await this.runAPITest(test);
        }
    }

    // 运行单个API测试
    async runAPITest(test) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const options = {
                hostname: 'localhost',
                port: 3000,
                path: test.path,
                method: test.method,
                timeout: TEST_CONFIG.TIMEOUT
            };

            const req = http.request(options, (res) => {
                const responseTime = Date.now() - startTime;
                let data = '';

                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    const testResult = {
                        name: test.name,
                        path: test.path,
                        method: test.method,
                        statusCode: res.statusCode,
                        responseTime,
                        success: res.statusCode >= 200 && res.statusCode < 300
                    };

                    if (testResult.success) {
                        log('SUCCESS', `${test.name} 测试通过`, { 
                            statusCode: res.statusCode, 
                            responseTime: `${responseTime}ms` 
                        });
                        this.testResults.passedTests++;
                    } else {
                        log('ERROR', `${test.name} 测试失败`, testResult);
                        this.testResults.failedTests++;
                    }

                    this.testResults.apiTests.push(testResult);
                    this.testResults.totalTests++;
                    resolve();
                });
            });

            req.on('error', (error) => {
                log('ERROR', `${test.name} 请求失败`, { error: error.message });
                this.testResults.apiTests.push({
                    name: test.name,
                    path: test.path,
                    method: test.method,
                    error: error.message,
                    success: false
                });
                this.testResults.failedTests++;
                this.testResults.totalTests++;
                resolve();
            });

            req.on('timeout', () => {
                log('ERROR', `${test.name} 请求超时`);
                req.destroy();
                this.testResults.failedTests++;
                this.testResults.totalTests++;
                resolve();
            });

            req.end();
        });
    }

    // 性能测试
    async testPerformance() {
        log('INFO', '⚡ 开始性能测试...');
        
        // 并发测试
        const concurrentRequests = 10;
        const promises = [];
        const startTime = Date.now();

        for (let i = 0; i < concurrentRequests; i++) {
            promises.push(this.runAPITest({ 
                name: `并发测试-${i+1}`, 
                path: '/music/health', 
                method: 'GET' 
            }));
        }

        await Promise.all(promises);
        const totalTime = Date.now() - startTime;

        this.testResults.performance = {
            concurrentRequests,
            totalTime,
            averageResponseTime: totalTime / concurrentRequests,
            requestsPerSecond: (concurrentRequests / totalTime) * 1000
        };

        log('SUCCESS', '性能测试完成', this.testResults.performance);
    }

    // 生成测试报告
    async generateTestReport() {
        log('INFO', '📊 生成测试报告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalTests: this.testResults.totalTests,
                passedTests: this.testResults.passedTests,
                failedTests: this.testResults.failedTests,
                successRate: ((this.testResults.passedTests / this.testResults.totalTests) * 100).toFixed(2) + '%'
            },
            details: this.testResults
        };

        // 保存JSON报告
        const reportPath = `test-report-${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        // 生成Markdown报告
        const markdownReport = this.generateMarkdownReport(report);
        const markdownPath = `test-report-${Date.now()}.md`;
        fs.writeFileSync(markdownPath, markdownReport);

        log('SUCCESS', '测试报告已生成', { 
            jsonReport: reportPath, 
            markdownReport: markdownPath 
        });

        // 输出测试总结
        this.printTestSummary(report);
    }

    // 生成Markdown测试报告
    generateMarkdownReport(report) {
        return `# 🧪 音乐解锁服务测试报告

## 📊 测试总结

- **测试时间**: ${report.timestamp}
- **总测试数**: ${report.summary.totalTests}
- **通过测试**: ${report.summary.passedTests}
- **失败测试**: ${report.summary.failedTests}
- **成功率**: ${report.summary.successRate}

## 📋 详细结果

### 配置验证
- **状态**: ${report.details.configValidation ? '✅ 通过' : '❌ 失败'}

### 服务启动
- **状态**: ${report.details.serverStart ? '✅ 通过' : '❌ 失败'}

### API测试
${report.details.apiTests.map(test => 
    `- **${test.name}**: ${test.success ? '✅ 通过' : '❌ 失败'} (${test.responseTime || 'N/A'}ms)`
).join('\n')}

### 性能测试
- **并发请求数**: ${report.details.performance.concurrentRequests || 'N/A'}
- **总耗时**: ${report.details.performance.totalTime || 'N/A'}ms
- **平均响应时间**: ${report.details.performance.averageResponseTime || 'N/A'}ms
- **每秒请求数**: ${report.details.performance.requestsPerSecond || 'N/A'}

## 🎯 测试结论

${report.summary.successRate === '100.00%' ? 
    '🎉 所有测试通过！服务运行正常。' : 
    '⚠️ 部分测试失败，请检查详细日志。'}
`;
    }

    // 打印测试总结
    printTestSummary(report) {
        log('HEADER', '🎯 测试总结');
        log('INFO', `总测试数: ${report.summary.totalTests}`);
        log('SUCCESS', `通过测试: ${report.summary.passedTests}`);
        log('ERROR', `失败测试: ${report.summary.failedTests}`);
        log('INFO', `成功率: ${report.summary.successRate}`);
        
        if (report.summary.successRate === '100.00%') {
            log('SUCCESS', '🎉 所有测试通过！服务运行正常。');
        } else {
            log('WARNING', '⚠️ 部分测试失败，请检查详细日志。');
        }
    }

    // 清理资源
    async cleanup() {
        log('INFO', '🧹 清理测试资源...');
        
        if (this.serverProcess) {
            this.serverProcess.kill('SIGTERM');
            log('INFO', '服务进程已终止');
        }
    }
}

// 主函数
async function main() {
    const runner = new TestRunner();
    await runner.runAllTests();
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = TestRunner;
