#!/usr/bin/env node

/**
 * 🧪 简化版音乐解锁服务测试脚本
 * 快速验证服务基本功能
 */

const http = require('http');
const { spawn } = require('child_process');

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m'
};

function log(level, message) {
    const levelColors = {
        INFO: colors.blue,
        SUCCESS: colors.green,
        ERROR: colors.red,
        WARNING: colors.yellow,
        HEADER: colors.magenta
    };
    
    const color = levelColors[level] || colors.reset;
    console.log(`${color}[${level}]${colors.reset} ${message}`);
}

class SimpleTest {
    constructor() {
        this.serverProcess = null;
        this.results = {
            configTest: false,
            serverStart: false,
            healthCheck: false,
            sourcesStatus: false
        };
    }

    async runTests() {
        log('HEADER', '🧪 开始简化版测试');
        
        try {
            // 1. 配置验证
            await this.testConfig();
            
            // 2. 启动服务
            await this.startServer();
            
            // 3. 基础API测试
            await this.testBasicAPIs();
            
            // 4. 生成报告
            this.generateReport();
            
        } catch (error) {
            log('ERROR', `测试失败: ${error.message}`);
        } finally {
            this.cleanup();
        }
    }

    async testConfig() {
        log('INFO', '📋 测试配置验证...');
        
        return new Promise((resolve) => {
            const { exec } = require('child_process');
            exec('node scripts/validate-config.js', (error, stdout, stderr) => {
                if (error) {
                    log('ERROR', '配置验证失败');
                    this.results.configTest = false;
                } else {
                    log('SUCCESS', '配置验证通过');
                    this.results.configTest = true;
                }
                resolve();
            });
        });
    }

    async startServer() {
        log('INFO', '🚀 启动服务...');
        
        return new Promise((resolve) => {
            this.serverProcess = spawn('npm', ['run', 'dev'], {
                stdio: ['pipe', 'pipe', 'pipe'],
                shell: true
            });

            const timeout = setTimeout(() => {
                log('ERROR', '服务启动超时');
                this.results.serverStart = false;
                resolve();
            }, 15000);

            this.serverProcess.stdout.on('data', (data) => {
                const output = data.toString();
                if (output.includes('音乐解锁服务启动成功')) {
                    clearTimeout(timeout);
                    log('SUCCESS', '服务启动成功');
                    this.results.serverStart = true;
                    setTimeout(resolve, 2000); // 等待服务完全就绪
                }
            });

            this.serverProcess.stderr.on('data', (data) => {
                const error = data.toString();
                if (error.includes('Error')) {
                    log('ERROR', `服务错误: ${error.trim()}`);
                }
            });
        });
    }

    async testBasicAPIs() {
        log('INFO', '🔌 测试基础API...');
        
        // 测试健康检查
        await this.testAPI('健康检查', '/music/health');
        
        // 测试音源状态
        await this.testAPI('音源状态', '/music/sources/status');
    }

    async testAPI(name, path) {
        return new Promise((resolve) => {
            const options = {
                hostname: 'localhost',
                port: 3000,
                path: path,
                method: 'GET',
                timeout: 10000
            };

            const req = http.request(options, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    if (res.statusCode >= 200 && res.statusCode < 300) {
                        log('SUCCESS', `${name} 测试通过 (${res.statusCode})`);
                        if (name === '健康检查') this.results.healthCheck = true;
                        if (name === '音源状态') this.results.sourcesStatus = true;
                    } else {
                        log('ERROR', `${name} 测试失败 (${res.statusCode})`);
                    }
                    resolve();
                });
            });

            req.on('error', (error) => {
                log('ERROR', `${name} 请求失败: ${error.message}`);
                resolve();
            });

            req.on('timeout', () => {
                log('ERROR', `${name} 请求超时`);
                req.destroy();
                resolve();
            });

            req.end();
        });
    }

    generateReport() {
        log('HEADER', '📊 测试报告');
        
        const tests = [
            { name: '配置验证', result: this.results.configTest },
            { name: '服务启动', result: this.results.serverStart },
            { name: '健康检查', result: this.results.healthCheck },
            { name: '音源状态', result: this.results.sourcesStatus }
        ];

        let passed = 0;
        let total = tests.length;

        tests.forEach(test => {
            const status = test.result ? '✅ 通过' : '❌ 失败';
            log('INFO', `${test.name}: ${status}`);
            if (test.result) passed++;
        });

        const successRate = ((passed / total) * 100).toFixed(1);
        log('INFO', `总体结果: ${passed}/${total} 通过 (${successRate}%)`);

        if (passed === total) {
            log('SUCCESS', '🎉 所有基础测试通过！');
        } else {
            log('WARNING', '⚠️ 部分测试失败，请检查服务配置');
        }

        // 保存简单报告
        const report = {
            timestamp: new Date().toISOString(),
            results: this.results,
            summary: {
                total,
                passed,
                failed: total - passed,
                successRate: successRate + '%'
            }
        };

        require('fs').writeFileSync(
            `simple-test-report-${Date.now()}.json`, 
            JSON.stringify(report, null, 2)
        );
        
        log('INFO', '测试报告已保存');
    }

    cleanup() {
        log('INFO', '🧹 清理资源...');
        if (this.serverProcess) {
            this.serverProcess.kill('SIGTERM');
            log('INFO', '服务进程已终止');
        }
    }
}

// 运行测试
async function main() {
    const test = new SimpleTest();
    await test.runTests();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = SimpleTest;
