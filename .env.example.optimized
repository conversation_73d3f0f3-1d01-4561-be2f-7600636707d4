# ===================================================================
# 🎵 音乐解锁服务环境配置文件
# 基于UnblockNeteaseMusic的音乐解锁服务后端配置
# 复制此文件为 .env 并根据需要修改配置值
# ===================================================================

# ===================================================================
# 🚀 服务基础配置 (Basic Service Configuration)
# ===================================================================

# 服务端口 (Service Port)
# 控制服务监听的端口号，默认3000
PORT=3000

# 服务主机地址 (Service Host)  
# 控制服务监听的主机地址，默认localhost
HOST=localhost

# 运行环境 (Runtime Environment)
# 控制应用运行模式，影响日志级别和安全策略
# 可选值: development, production, test
NODE_ENV=development

# ===================================================================
# ⏱️ 超时控制配置 (Timeout Configuration)
# ===================================================================

# 解锁请求超时时间 (Unlock Request Timeout)
# 控制单首歌曲解锁的最大等待时间，单位毫秒
# 建议范围: 10000-60000 (10秒-60秒)
UNLOCK_TIMEOUT=30000

# 音源测试超时时间 (Source Test Timeout)
# 控制音源可用性测试的最大等待时间，单位毫秒
# 建议范围: 5000-15000 (5秒-15秒)
SOURCE_TEST_TIMEOUT=10000

# 请求超时时间 (Request Timeout)
# 控制API请求的最大等待时间，单位毫秒
# 建议范围: 10000-60000 (10秒-60秒)
REQUEST_TIMEOUT=30000

# ===================================================================
# 🎛️ 性能控制配置 (Performance Configuration)
# ===================================================================

# 批量处理并发数 (Batch Concurrency)
# 控制批量解锁时的并发处理数量，影响服务器负载和处理速度
# 建议范围: 3-10，根据服务器性能调整
BATCH_CONCURRENCY=5

# ===================================================================
# 🔒 安全配置 (Security Configuration)
# ===================================================================

# 请求频率限制 - 最大请求数 (Rate Limit Max Requests)
# 控制单个IP在时间窗口内的最大请求数
# 建议范围: 50-500，根据业务需求调整
RATE_LIMIT_MAX_REQUESTS=100

# 请求频率限制 - 时间窗口 (Rate Limit Window)
# 控制频率限制的时间窗口，单位毫秒
# 默认15分钟 (900000ms)
RATE_LIMIT_WINDOW_MS=900000

# CORS跨域配置 (CORS Configuration)
# 控制允许跨域访问的源，生产环境请设置具体域名
# 开发环境: * | 生产环境: https://yourdomain.com
CORS_ORIGIN=*

# 会话密钥 (Session Secret)
# 用于会话加密的密钥，生产环境必须设置强密码
# 建议使用32位以上随机字符串
SESSION_SECRET=change-this-in-production

# 最大请求大小 (Max Request Size)
# 控制HTTP请求体的最大大小，防止大文件攻击
# 支持格式: 10mb, 1gb 等
MAX_REQUEST_SIZE=10mb

# ===================================================================
# 🎵 音乐服务配置 (Music Service Configuration)
# ===================================================================

# 音源优先级配置 (Music Sources Priority)
# 控制音源的优先级顺序，逗号分隔
# 可用音源: migu,kuwo,qq,kugou,joox,youtube
MUSIC_SOURCES=migu,kuwo,qq,kugou,joox,youtube

# 启用无损音质 (Enable FLAC Quality)
# 控制是否启用无损音质支持，true启用/false禁用
ENABLE_FLAC=true

# 启用本地VIP功能 (Enable Local VIP)
# 控制是否启用本地VIP功能，可能获得更高音质
# true启用/false禁用
ENABLE_LOCAL_VIP=true

# 音源选择策略 (Source Selection Strategy)
# 控制音源选择策略
# false: 并行模式，速度快但音源不确定
# true: 顺序模式，按优先级逐个尝试
FOLLOW_SOURCE_ORDER=false

# 广告屏蔽功能 (Block Ads)
# 控制是否屏蔽音频中的广告内容
# true启用/false禁用
BLOCK_ADS=true

# ===================================================================
# 🔑 音源认证配置 (Music Source Authentication)
# ===================================================================

# 网易云音乐Cookie (Netease Cookie)
# 用于网易云音乐的用户认证，可提高解锁成功率
NETEASE_COOKIE=

# QQ音乐Cookie (QQ Music Cookie)
# 用于QQ音乐的用户认证，可提高解锁成功率
QQ_COOKIE=

# 咪咕音乐Cookie (Migu Cookie)
# 用于咪咕音乐的用户认证，可提高解锁成功率
MIGU_COOKIE=

# JOOX音乐Cookie (JOOX Cookie)
# 用于JOOX音乐的用户认证，可提高解锁成功率
JOOX_COOKIE=

# YouTube API密钥 (YouTube API Key)
# 用于YouTube音源的API访问，需要申请Google API密钥
YOUTUBE_KEY=

# ===================================================================
# 🌐 网络配置 (Network Configuration)
# ===================================================================

# 代理服务器配置 (Proxy Configuration)
# HTTP/HTTPS代理服务器地址，格式: http://proxy.example.com:8080
# 留空表示不使用代理
PROXY_URL=

# 自定义域名解析 (Custom Hosts)
# 自定义域名解析配置，JSON格式
# 示例: {"music.163.com": "*******"}
CUSTOM_HOSTS={}

# ===================================================================
# 📊 API限制配置 (API Limits Configuration)
# ===================================================================

# 批量操作最大数量 (Max Batch Size)
# 控制单次批量操作的最大歌曲数量，防止服务器过载
# 建议范围: 10-50
MAX_BATCH_SIZE=20

# 最大搜索结果数 (Max Search Results)
# 控制搜索接口返回的最大结果数量
# 建议范围: 20-100
MAX_SEARCH_RESULTS=50

# 搜索关键词最大长度 (Max Keyword Length)
# 控制搜索关键词的最大字符长度，防止恶意请求
# 建议范围: 50-200
MAX_KEYWORD_LENGTH=100

# ===================================================================
# 📝 日志配置 (Logging Configuration)
# ===================================================================

# 日志级别 (Log Level)
# 控制日志输出的详细程度
# 可选值: error, warn, info, debug
LOG_LEVEL=info

# 启用文件日志 (Enable File Logging)
# 控制是否将日志写入文件，true启用/false禁用
LOG_FILE_ENABLED=true

# 启用控制台日志 (Enable Console Logging)
# 控制是否在控制台输出日志，true启用/false禁用
LOG_CONSOLE_ENABLED=true

# ===================================================================
# ⚠️ 已移除的配置项说明
# ===================================================================
# 以下配置项已被移除，因为在代码中没有实际功能实现：
#
# 缓存配置 (已移除 - 无实际缓存实现):
# - CACHE_METADATA_TTL
# - CACHE_SEARCH_TTL  
# - CACHE_UNLOCK_TTL
# - CACHE_ENABLED
#
# 重试配置 (已移除 - 无重试逻辑实现):
# - MAX_RETRIES
# - RETRY_DELAY
#
# 重复配置 (已合并):
# - API_REQUEST_TIMEOUT (已合并到 REQUEST_TIMEOUT)
# ===================================================================
