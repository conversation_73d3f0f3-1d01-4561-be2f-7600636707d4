/**
 * 音乐元数据服务模块
 * 提供音乐信息获取、元数据处理等功能
 */

const { unlockSong } = require('./unlockService');
const { logError, logPerformance, logBusiness } = require('../middleware/logger');
const { NotFoundError, ServiceUnavailableError } = require('../middleware/errorHandler');
const { QUALITY_DISPLAY_NAMES } = require('../utils/constants');

/**
 * 获取歌曲完整信息（包含解锁链接）
 * @param {number|string} songId - 歌曲ID
 * @param {Array<string>} sources - 指定音源
 * @returns {Promise<Object>} 歌曲完整信息
 */
async function getSongInfo(songId, sources = null) {
    const startTime = Date.now();
  
    try {
        logBusiness('获取歌曲信息', { songId, sources });

        // 解锁歌曲获取音频信息
        const unlockResult = await unlockSong(songId, sources);
    
        // 合并信息 - 只保留有实际价值的字段，使用中文字段名
        const songInfo = {
            歌曲ID: parseInt(songId),

            // 音频信息
            音频信息: {
                播放链接: unlockResult.播放链接,
                音质: unlockResult.音质,
                音质描述: getQualityLevel(unlockResult.音质),
                格式: unlockResult.格式,
                文件大小: unlockResult.文件大小
            },

            // 音源信息
            音源信息: {
                音源ID: unlockResult.音源ID,
                音源名称: unlockResult.音源名称,
                可用状态: true
            },

            // 时间戳
            解锁时间: unlockResult.解锁时间
        };

        const duration = Date.now() - startTime;
        logPerformance('获取歌曲信息', duration, { songId, success: true });
    
        logBusiness('歌曲信息获取成功', {
            songId,
            source: songInfo.音源信息.音源ID,
            bitrate: songInfo.音频信息.音质
        });

        return songInfo;

    } catch (error) {
        const duration = Date.now() - startTime;
        logPerformance('获取歌曲信息失败', duration, { songId, error: error.message });
    
        if (error instanceof NotFoundError) {
            throw error;
        }
    
        logError(error, { context: 'get_song_info', songId });
        throw new ServiceUnavailableError(`获取歌曲 ${songId} 信息失败: ${error.message}`);
    }
}







/**
 * 根据比特率获取音质等级
 * @param {number} bitrate - 比特率
 * @returns {string} 音质等级描述
 */
function getQualityLevel(bitrate) {
    if (bitrate >= 999000) return QUALITY_DISPLAY_NAMES[999000] || '无损';
    if (bitrate >= 320000) return QUALITY_DISPLAY_NAMES[320000] || '极高';
    if (bitrate >= 192000) return QUALITY_DISPLAY_NAMES[192000] || '较高';
    if (bitrate >= 128000) return QUALITY_DISPLAY_NAMES[128000] || '标准';
    return '低质量';
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
function formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '未知';
  
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
  
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
  
    return `${size.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * 格式化时长
 * @param {number} duration - 时长（秒）
 * @returns {string} 格式化后的时长
 */
function formatDuration(duration) {
    if (!duration || duration === 0) return '00:00';
  
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
  
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

/**
 * 验证歌曲ID格式
 * @param {any} songId - 歌曲ID
 * @returns {boolean} 是否有效
 */
function isValidSongId(songId) {
    const id = parseInt(songId);
    return !isNaN(id) && id > 0 && id <= Number.MAX_SAFE_INTEGER;
}

/**
 * 批量获取歌曲信息
 * @param {Array<number|string>} songIds - 歌曲ID列表
 * @param {Array<string>} sources - 指定音源
 * @param {Object} options - 选项
 * @returns {Promise<Object>} 批量结果
 */
async function getSongsInfoBatch(songIds, sources = null, options = {}) {
    const startTime = Date.now();
  
    logBusiness('批量获取歌曲信息', { count: songIds.length, sources, options });

    const results = {
        success: [],
        failed: [],
        total: songIds.length,
        successCount: 0,
        failedCount: 0
    };

    // 并发处理，限制并发数
    const concurrency = 3;
    const chunks = [];
    for (let i = 0; i < songIds.length; i += concurrency) {
        chunks.push(songIds.slice(i, i + concurrency));
    }

    for (const chunk of chunks) {
        const promises = chunk.map(async (songId) => {
            try {
                // 现在只支持获取完整歌曲信息
                const songInfo = await getSongInfo(songId, sources);
          
                results.success.push(songInfo);
                results.successCount++;
        
            } catch (error) {
                results.failed.push({
                    歌曲ID: parseInt(songId),
                    错误信息: error.message,
                    错误代码: error.errorCode || 'GET_INFO_FAILED'
                });
                results.failedCount++;
            }
        });

        await Promise.all(promises);
    }

    const duration = Date.now() - startTime;
    logPerformance('批量获取歌曲信息完成', duration, {
        total: results.total,
        success: results.successCount,
        failed: results.failedCount
    });

    return results;
}

module.exports = {
    getSongInfo,
    getSongsInfoBatch,
    getQualityLevel,
    formatFileSize,
    formatDuration,
    isValidSongId
};
