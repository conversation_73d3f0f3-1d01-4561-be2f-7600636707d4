/**
 * 错误处理中间件模块
 * 统一处理应用中的各种错误和异常情况
 */

const { logError } = require('./logger');
// const { internalError, serviceUnavailable } = require('../utils/response');
const { HTTP_STATUS, ERROR_CODES } = require('../utils/constants');

/**
 * 自定义错误类
 */
class AppError extends Error {
    constructor(message, statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR, errorCode = ERROR_CODES.INTERNAL_ERROR) {
        super(message);
        this.statusCode = statusCode;
        this.errorCode = errorCode;
        this.isOperational = true;

        Error.captureStackTrace(this, this.constructor);
    }
}

/**
 * 业务错误类
 */
class BusinessError extends AppError {
    constructor(message, errorCode) {
        super(message, HTTP_STATUS.BAD_REQUEST, errorCode);
    }
}

/**
 * 资源未找到错误类
 */
class NotFoundError extends AppError {
    constructor(message = '资源未找到') {
        super(message, HTTP_STATUS.NOT_FOUND, ERROR_CODES.SONG_NOT_FOUND);
    }
}

/**
 * 验证错误类
 */
class ValidationError extends AppError {
    constructor(message, details = null) {
        super(message, HTTP_STATUS.BAD_REQUEST, ERROR_CODES.VALIDATION_ERROR);
        this.details = details;
    }
}

/**
 * 服务不可用错误类
 */
class ServiceUnavailableError extends AppError {
    constructor(message = '服务暂时不可用') {
        super(message, HTTP_STATUS.SERVICE_UNAVAILABLE, ERROR_CODES.SOURCE_UNAVAILABLE);
    }
}

/**
 * 处理UnblockNeteaseMusic相关错误
 * @param {Error} error - 原始错误
 * @returns {AppError} 转换后的应用错误
 */
function handleUnblockError(error) {
    const message = error.message || '音乐解锁服务出错';
  
    // 根据错误消息判断错误类型
    if (message.includes('timeout') || message.includes('TIMEOUT')) {
        return new ServiceUnavailableError('音乐服务响应超时');
    }
  
    if (message.includes('not found') || message.includes('NOT_FOUND')) {
        return new NotFoundError('歌曲未找到');
    }
  
    if (message.includes('network') || message.includes('NETWORK')) {
        return new ServiceUnavailableError('网络连接错误');
    }
  
    return new AppError(message, HTTP_STATUS.INTERNAL_SERVER_ERROR, ERROR_CODES.UNLOCK_FAILED);
}

/**
 * 处理数据库错误
 * @param {Error} error - 数据库错误
 * @returns {AppError} 转换后的应用错误
 */
// function handleDatabaseError(error) {
//     logError(error, { type: 'database_error' });
//     return new AppError('数据库操作失败', HTTP_STATUS.INTERNAL_SERVER_ERROR, ERROR_CODES.INTERNAL_ERROR);
// }

/**
 * 处理网络请求错误
 * @param {Error} error - 网络错误
 * @returns {AppError} 转换后的应用错误
 */
function handleNetworkError(error) {
    if (error.code === 'ECONNREFUSED') {
        return new ServiceUnavailableError('无法连接到音乐服务');
    }
  
    if (error.code === 'ETIMEDOUT') {
        return new ServiceUnavailableError('请求超时');
    }
  
    return new ServiceUnavailableError('网络请求失败');
}

/**
 * 错误处理中间件
 * @param {Error} err - 错误对象
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
function errorHandler(err, req, res, next) {
    // 如果响应已经发送，则交给默认的Express错误处理器
    if (res.headersSent) {
        return next(err);
    }

    let error = err;

    // 如果不是自定义错误，进行转换
    if (!(error instanceof AppError)) {
        // 处理不同类型的错误
        if (error.name === 'ValidationError') {
            error = new ValidationError(error.message, error.details);
        } else if (error.name === 'CastError') {
            error = new ValidationError('无效的参数格式');
        } else if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
            error = new ValidationError('JSON格式错误');
        } else if (error.name === 'TypeError' && error.message.includes('Cannot read')) {
            error = new ValidationError('参数缺失或格式错误');
        } else if (error.code && error.code.startsWith('E')) {
            error = handleNetworkError(error);
        } else if (error.message && error.message.includes('unblock')) {
            error = handleUnblockError(error);
        } else if (error.name === 'TimeoutError') {
            error = new ServiceUnavailableError('请求超时，请稍后重试');
        } else {
            error = new AppError(error.message || '服务器内部错误');
        }
    }

    // 记录错误日志
    logError(error, {
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId: req.id,
        originalError: err.name
    });

    // 发送错误响应 - 使用中文字段名保持一致性
    res.status(error.statusCode).json({
        状态码: error.statusCode,
        消息: error.message,
        时间戳: new Date().toISOString(),
        数据: null,
        错误代码: error.errorCode,
        ...(error.details && { 详情: error.details }),
        ...(process.env.NODE_ENV === 'development' && {
            堆栈: error.stack,
            原始错误: err.message
        })
    });
}

/**
 * 404错误处理中间件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
function notFoundHandler(req, res) {
    // 提供更友好的错误信息和建议
    let suggestion = '';
    const path = req.url.toLowerCase();

    if (path.includes('/api/')) {
        suggestion = '提示：/api/* 路径已废弃，请使用 /music/* 路径';
    } else if (path.includes('/health')) {
        suggestion = '提示：健康检查端点已移除，请访问根路径 / 获取服务状态';
    } else if (path.includes('/music/')) {
        suggestion = '提示：请检查路径是否正确，支持的端点：/music/unlock, /music/source';
    } else {
        suggestion = '提示：请访问根路径 / 查看API文档';
    }

    // 直接返回JSON格式的404错误响应，保持格式一致性
    const errorMessage = `路由 ${req.method} ${req.url} 未找到。${suggestion}`;

    // 记录错误日志
    logError(new NotFoundError(errorMessage), {
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId: req.id
    });

    // 返回统一格式的JSON错误响应
    res.status(404).json({
        状态码: 404,
        消息: errorMessage,
        时间戳: new Date().toISOString(),
        数据: null,
        错误代码: 'NOT_FOUND'
    });
}

/**
 * 异步错误包装器
 * 用于包装异步路由处理函数，自动捕获Promise拒绝
 * @param {Function} fn - 异步函数
 * @returns {Function} 包装后的函数
 */
function asyncHandler(fn) {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
}

/**
 * 进程级错误处理
 */
function setupProcessErrorHandlers() {
    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
        logError(error, { type: 'uncaught_exception' });
        console.error('未捕获的异常:', error);
        process.exit(1);
    });

    // 处理未处理的Promise拒绝
    process.on('unhandledRejection', (reason, promise) => {
        logError(new Error(reason), { 
            type: 'unhandled_rejection',
            promise: promise.toString()
        });
        console.error('未处理的Promise拒绝:', reason);
    });
}

module.exports = {
    AppError,
    BusinessError,
    NotFoundError,
    ValidationError,
    ServiceUnavailableError,
    errorHandler,
    notFoundHandler,
    asyncHandler,
    setupProcessErrorHandlers
};
