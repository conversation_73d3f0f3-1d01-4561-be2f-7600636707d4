# 🚀 音乐解锁服务 - CI/CD 工作流
# 自动化构建、测试、部署流水线

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18.x'
  CACHE_KEY: node-modules-${{ hashFiles('**/package-lock.json') }}

jobs:
  # 🔍 代码质量检查
  lint:
    name: 代码质量检查
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: ESLint 检查
      run: npm run lint
      
    - name: 代码格式检查
      run: npm run format:check || true

  # 🧪 单元测试
  test:
    name: 单元测试
    runs-on: ubuntu-latest
    needs: lint
    
    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]
        
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 运行单元测试
      run: npm run test:unit
      
    - name: 生成覆盖率报告
      run: npm run test:coverage
      
    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

  # 🔗 集成测试
  integration:
    name: 集成测试
    runs-on: ubuntu-latest
    needs: test
    
    services:
      # 如果需要数据库服务，可以在这里添加
      redis:
        image: redis:alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 运行集成测试
      run: npm run test:integration
      env:
        NODE_ENV: test
        PORT: 3001

  # 🌐 E2E测试
  e2e:
    name: E2E测试
    runs-on: ubuntu-latest
    needs: integration
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 安装 Playwright
      run: npx playwright install --with-deps
      
    - name: 启动服务
      run: |
        npm start &
        sleep 10
      env:
        NODE_ENV: test
        PORT: 3000
        
    - name: 运行 E2E 测试
      run: npm run test:e2e
      
    - name: 上传测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30

  # ⚡ 性能测试
  performance:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: integration
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 启动服务
      run: |
        npm start &
        sleep 10
      env:
        NODE_ENV: production
        PORT: 3000
        
    - name: 运行性能测试
      run: npm run test:performance
      
    - name: 上传性能报告
      uses: actions/upload-artifact@v3
      with:
        name: performance-report
        path: performance-report.json

  # 🔒 安全扫描
  security:
    name: 安全扫描
    runs-on: ubuntu-latest
    needs: lint
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 运行安全审计
      run: npm audit --audit-level moderate
      
    - name: 运行 Snyk 安全扫描
      uses: snyk/actions/node@master
      continue-on-error: true
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

  # 🐳 Docker构建
  docker:
    name: Docker构建
    runs-on: ubuntu-latest
    needs: [test, integration]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: 登录 Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        
    - name: 构建并推送 Docker 镜像
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: |
          music-unlock-server:latest
          music-unlock-server:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 📊 质量门禁
  quality-gate:
    name: 质量门禁
    runs-on: ubuntu-latest
    needs: [lint, test, integration, e2e, security]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: 安装依赖
      run: npm ci
      
    - name: 生成质量报告
      run: |
        echo "# 🎯 质量门禁报告" > quality-report.md
        echo "" >> quality-report.md
        echo "## 📊 测试结果" >> quality-report.md
        echo "- ✅ 代码质量检查: 通过" >> quality-report.md
        echo "- ✅ 单元测试: 通过" >> quality-report.md
        echo "- ✅ 集成测试: 通过" >> quality-report.md
        echo "- ✅ E2E测试: 通过" >> quality-report.md
        echo "- ✅ 安全扫描: 通过" >> quality-report.md
        echo "" >> quality-report.md
        echo "## 🎉 质量门禁: **通过**" >> quality-report.md
        
    - name: 上传质量报告
      uses: actions/upload-artifact@v3
      with:
        name: quality-report
        path: quality-report.md

  # 🚀 部署到测试环境
  deploy-staging:
    name: 部署到测试环境
    runs-on: ubuntu-latest
    needs: quality-gate
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 部署到测试环境
      run: |
        echo "🚀 部署到测试环境..."
        echo "环境: staging"
        echo "版本: ${{ github.sha }}"
        # 这里添加实际的部署脚本

  # 🌟 部署到生产环境
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: quality-gate
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 部署到生产环境
      run: |
        echo "🌟 部署到生产环境..."
        echo "环境: production"
        echo "版本: ${{ github.sha }}"
        # 这里添加实际的部署脚本
        
    - name: 发送部署通知
      run: |
        echo "📢 部署完成通知"
        echo "项目: 音乐解锁服务"
        echo "版本: ${{ github.sha }}"
        echo "状态: 成功"
