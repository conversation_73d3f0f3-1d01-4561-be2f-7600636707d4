/**
 * 音乐解锁服务API测试脚本
 * 使用Jest进行API接口测试
 */

const request = require('supertest');
const app = require('../src/app');
const config = require('../src/config/config');

// 测试配置 (P2硬编码优化)
const TEST_SONG_ID = config.testing.defaultSongId; // 从配置获取
const TEST_SONG_IDS = config.testing.defaultSongIds; // 从配置获取
const API_BASE = '/api';

describe('音乐解锁服务API测试', () => {
    
    // 健康检查测试
    describe('健康检查', () => {
        test('GET /health - 应该返回服务健康状态', async () => {
            const response = await request(app)
                .get('/health')
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据.status).toBe('healthy');
        });
    });

    // API信息测试
    describe('API信息', () => {
        test('GET /api - 应该返回API基本信息', async () => {
            const response = await request(app)
                .get(API_BASE)
                .expect(200);

            expect(response.body.name).toBe('音乐解锁服务API');
            expect(response.body.version).toBeDefined();
        });

        test('GET /api/docs - 应该返回API文档', async () => {
            const response = await request(app)
                .get(`${API_BASE}/docs`)
                .expect(200);

            expect(response.body.title).toBe('音乐解锁服务API文档');
            expect(response.body.endpoints).toBeDefined();
        });
    });

    // 万能解锁API测试
    describe('万能解锁API', () => {
        test('GET /api/unlock?songIds=:id&mode=detail - 应该返回歌曲详细信息', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=detail&format=full`)
                .expect(200);

            expect(response.body.code).toBe(200);
            expect(response.body.data.歌曲ID).toBe(parseInt(TEST_SONG_ID));
        });

        test('GET /api/unlock?songIds=:id&mode=status - 应该检查歌曲状态', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=status&format=head`)
                .expect(200);

            expect(response.body.code).toBe(200);
            expect(response.body.data).toBeDefined();
        });

        test('GET /api/unlock?songIds=:id&mode=sources - 应该返回歌曲音源列表', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=sources&format=full`)
                .expect(200);

            expect(response.body.code).toBe(200);
            expect(response.body.data).toBeDefined();
        });

        test('GET /api/unlock?songIds=:id&mode=unlock - 应该返回解锁信息', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=unlock&format=full`)
                .expect(200);

            expect(response.body.code).toBe(200);
            expect(response.body.data.歌曲ID).toBe(parseInt(TEST_SONG_ID));
        });
    });

    // 搜索API测试 - 已删除，搜索功能不在当前API范围内

    // 解锁API测试
    describe('解锁API', () => {
        test('GET /api/unlock?songIds=:id&mode=unlock - 批量解锁', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=unlock&format=full&minBitrate=128000`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据.歌曲ID).toBe(parseInt(TEST_SONG_ID));
        });

        test('GET /api/unlock?songIds=:id&mode=unlock&minBitrate=128000 - 单首解锁', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=unlock&format=full&minBitrate=128000`)
                .expect(200);

            expect(response.body.code).toBe(200);
            expect(response.body.data.歌曲ID).toBe(parseInt(TEST_SONG_ID));
        });

        test('GET /api/unlock?songIds=:id&mode=unlock&format=minimal - 快速解锁', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=unlock&format=minimal`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据.歌曲ID).toBe(parseInt(TEST_SONG_ID));
        });

        test('GET /api/unlock?songIds=:id&mode=status - 检查解锁状态', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=status&format=full`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据).toBeDefined();
        });

        test('GET /api/unlock?songIds=:id1,:id2&mode=status - 批量检查解锁状态', async () => {
            const batchIds = TEST_SONG_IDS.slice(0, 2).join(','); // 使用配置中的前两个ID
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=${batchIds}&mode=status&format=full`)
                .expect(200);

            expect(response.body.code).toBe(200);
            expect(response.body.data).toBeDefined();
        });
    });

    // 音源管理API测试
    describe('音源管理API', () => {
        test('GET /api/sources - 获取音源列表', async () => {
            const response = await request(app)
                .get(`${API_BASE}/sources`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(Array.isArray(response.body.数据.音源列表)).toBe(true);
        });

        test('GET /api/sources/:sourceId - 获取音源详情', async () => {
            const response = await request(app)
                .get(`${API_BASE}/sources/qq`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据.音源ID).toBe('qq');
        });

        test('GET /api/unlock?mode=test&sources=qq - 测试单个音源', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?mode=test&sources=qq`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据.测试结果).toBeDefined();
        });

        test('GET /api/unlock?mode=test&sources=qq,kugou - 批量测试音源', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?mode=test&sources=qq,kugou`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据.测试结果).toBeDefined();
        });

        test('GET /api/sources/stats - 获取音源统计', async () => {
            const response = await request(app)
                .get(`${API_BASE}/sources/stats`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据.总音源数).toBeDefined();
        });

        test('GET /api/sources/config - 获取音源配置', async () => {
            const response = await request(app)
                .get(`${API_BASE}/sources/config`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据.音源配置).toBeDefined();
        });
    });

    // 错误处理测试
    describe('错误处理', () => {
        test('GET /api/unlock?songIds=invalid - 无效歌曲ID应返回400', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=invalid&mode=unlock`)
                .expect(400);

            expect(response.body.状态码).toBe(400);
            expect(response.body.错误.代码).toBe('VALIDATION_ERROR');
        });

        test('GET /api/nonexistent - 不存在的路径应返回404', async () => {
            const response = await request(app)
                .get(`${API_BASE}/nonexistent`)
                .expect(404);

            expect(response.body.状态码).toBe(404);
            expect(response.body.错误.代码).toBe('NOT_FOUND');
        });


    });
});

// 性能测试
describe('性能测试', () => {
    test('API响应时间应在合理范围内', async () => {
        const startTime = Date.now();

        await request(app)
            .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=unlock&format=full`)
            .expect(200);

        const responseTime = Date.now() - startTime;
        expect(responseTime).toBeLessThan(5000); // 5秒内响应
    });

    test('并发请求处理', async () => {
        const promises = Array(10).fill().map(() =>
            request(app)
                .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=unlock&format=full`)
                .expect(200)
        );

        const results = await Promise.all(promises);
        expect(results.length).toBe(10);
        results.forEach(result => {
            expect(result.body.code).toBe(200);
        });
    });
});
