# 🎵 音乐解锁服务 API 完整文档

**版本**: v1.0.0 | **状态**: ✅ 生产就绪 | **最后更新**: 2025-08-01 20:11

---

## 📋 **API 概览**

### **基础信息**
- **服务地址**: http://localhost:50091
- **API基础路径**: http://localhost:50091/api
- **响应格式**: JSON (中文字段名)
- **字符编码**: UTF-8
- **API端点总数**: 8个

### **功能分组**
| 分组 | 端点数量 | 主要功能 |
|------|----------|----------|
| 🏥 系统级API | 3个 | 健康检查、API信息、文档 |
| 🎵 万能解锁API | 1个 | 音乐解锁服务 (5种模式) |
| 📊 音源管理API | 4个 | 音源列表、统计、配置、详情 |

---

## 🏥 **系统级 API (3个)**

### 1. 健康检查 + API文档概览
```
GET /
```

**功能**: 服务健康检查和API概览信息

**请求参数**: 无

**响应示例**:
```json
{
  "状态码": 200,
  "消息": "音乐解锁服务运行正常",
  "时间戳": "2025-08-01T12:11:32.000Z",
  "数据": {
    "status": "healthy",
    "version": "1.0.0",
    "environment": "production",
    "uptime": 3600.5,
    "name": "音乐解锁服务API",
    "description": "基于UnblockNeteaseMusic的音乐解锁服务后端API",
    "baseUrl": "http://localhost:50091",
    "endpoints": {
      "根路径(健康检查+文档)": "/",
      "API信息": "/api/",
      "API详细文档": "/api/docs",
      "万能解锁服务": "/api/unlock",
      "音源列表": "/api/sources",
      "音源统计": "/api/sources/stats",
      "音源配置": "/api/sources/config",
      "单个音源详情": "/api/sources/:sourceId"
    },
    "usage": {
      "健康检查": "访问此根路径即可获取服务状态",
      "API测试": "使用独立的HTML测试工具进行API测试",
      "详细文档": "访问 /api/docs 获取完整API文档"
    }
  }
}
```

### 2. API基本信息
```
GET /api/
```

**功能**: 获取API基本信息和端点列表

**请求参数**: 无

**响应示例**:
```json
{
  "name": "音乐解锁服务API",
  "version": "1.0.0",
  "description": "基于UnblockNeteaseMusic的音乐解锁服务后端API",
  "endpoints": {
    "解锁": "/api/unlock",
    "音源": "/api/sources"
  },
  "documentation": "/api/docs",
  "health": "/",
  "timestamp": "2025-08-01T12:11:32.000Z"
}
```

### 3. 完整API文档
```
GET /api/docs
```

**功能**: 获取完整的API文档和使用示例

**请求参数**: 无

**响应**: 包含完整的API文档结构、参数说明和使用示例

---

## 🎵 **万能解锁 API (1个) ⭐**

### 万能解锁服务
```
GET /api/unlock
```

**功能**: 提供5种操作模式的音乐解锁服务

#### **请求参数**
| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `songIds` | string | ✅ | - | 歌曲ID，支持单个或逗号分隔的多个ID |
| `mode` | string | ❌ | `unlock` | 操作模式: `unlock`/`status`/`test`/`sources`/`detail` |
| `format` | string | ❌ | `full` | 返回格式: `full`/`minimal`/`head` |
| `detailed` | string | ❌ | `true` | 是否返回详细信息: `true`/`false` |
| `sources` | string | ❌ | 配置默认 | 指定音源列表，逗号分隔 |
| `minBitrate` | string | ❌ | `128000` | 最低音质要求 (bps) |
| `testSongId` | string | ❌ | `418602084` | 测试模式下使用的歌曲ID |

#### **操作模式详解**

##### 1. unlock模式 (默认) - 音乐解锁
**功能**: 执行音乐解锁操作，获取播放链接

**示例请求**:
```bash
curl "http://localhost:50091/api/unlock?songIds=418602084"
```

**响应示例**:
```json
{
  "状态码": 200,
  "消息": "解锁1首歌曲完成",
  "时间戳": "2025-08-01T12:11:32.000Z",
  "数据": {
    "歌曲ID": 418602084,
    "歌曲名": "海阔天空",
    "艺术家": "Beyond",
    "播放链接": "http://music.migu.cn/v3/music/player/audio?...",
    "音源ID": "migu",
    "音源名称": "咪咕音乐",
    "音质": 320000,
    "文件大小": 8456789,
    "格式": "mp3",
    "解锁时间": "2025-08-01T12:11:32.000Z"
  }
}
```

##### 2. status模式 - 状态检查
**功能**: 检查歌曲的可用性状态，不执行实际解锁

**示例请求**:
```bash
curl "http://localhost:50091/api/unlock?songIds=418602084&mode=status"
```

**响应示例**:
```json
{
  "状态码": 200,
  "消息": "检查1首歌曲状态完成",
  "时间戳": "2025-08-01T12:11:32.000Z",
  "数据": [
    {
      "歌曲ID": 418602084,
      "状态": "可用",
      "音源": "migu",
      "检查时间": "2025-08-01T12:11:32.000Z"
    }
  ]
}
```

##### 3. detail模式 - 详细信息
**功能**: 获取歌曲的完整元数据信息

**示例请求**:
```bash
curl "http://localhost:50091/api/unlock?songIds=418602084&mode=detail"
```

**响应示例**:
```json
{
  "状态码": 200,
  "消息": "获取1首歌曲详情完成",
  "时间戳": "2025-08-01T12:11:32.000Z",
  "数据": {
    "歌曲ID": 418602084,
    "音频信息": {
      "播放链接": "http://...",
      "音质": 320000,
      "音质描述": "极高",
      "格式": "mp3",
      "文件大小": 8456789
    },
    "音源信息": {
      "音源ID": "migu",
      "音源名称": "咪咕音乐",
      "可用状态": true
    },
    "解锁时间": "2025-08-01T12:11:32.000Z"
  }
}
```

##### 4. sources模式 - 音源分析 🚀 (已优化并行处理)
**功能**: 获取歌曲在各音源的可用性信息

**示例请求**:
```bash
curl "http://localhost:50091/api/unlock?songIds=418602084&mode=sources"
```

**响应示例**:
```json
{
  "状态码": 200,
  "消息": "获取1首歌曲音源信息完成",
  "时间戳": "2025-08-01T12:11:32.000Z",
  "数据": [
    {
      "歌曲ID": 418602084,
      "可用音源": [
        {
          "音源ID": "kuwo",
          "音质": 999000,
          "比特率": 0
        },
        {
          "音源ID": "migu",
          "音质": 128000,
          "比特率": 0
        }
      ],
      "音源数量": 2
    }
  ]
}
```

##### 5. test模式 - 连通性测试
**功能**: 测试音源连通性

**示例请求**:
```bash
curl "http://localhost:50091/api/unlock?mode=test"
```

**响应示例**:
```json
{
  "状态码": 200,
  "消息": "音源测试完成",
  "时间戳": "2025-08-01T12:11:32.000Z",
  "数据": [
    {
      "音源ID": "migu",
      "状态": "可用",
      "测试歌曲ID": 418602084,
      "测试时间": "2025-08-01T12:11:32.000Z"
    },
    {
      "音源ID": "qq",
      "状态": "不可用",
      "测试歌曲ID": 418602084,
      "测试时间": "2025-08-01T12:11:32.000Z"
    }
  ]
}
```

#### **批量操作**
**最大批量数量**: 20首歌曲

**批量请求示例**:
```bash
curl "http://localhost:50091/api/unlock?songIds=418602084,185868,1297742167"
```

**批量响应示例**:
```json
{
  "状态码": 200,
  "消息": "批量解锁完成，成功: 2/3",
  "时间戳": "2025-08-01T12:11:32.000Z",
  "数据": {
    "成功列表": [
      {
        "歌曲ID": 418602084,
        "播放链接": "http://...",
        "音源ID": "migu"
      }
    ],
    "失败列表": [
      {
        "歌曲ID": 185868,
        "错误信息": "未找到可用音源"
      }
    ],
    "总数": 3,
    "成功数量": 2,
    "失败数量": 1,
    "统计信息": {
      "成功率": "66.67%"
    }
  }
}
```

---

## 📊 **音源管理 API (4个)**

### 1. 音源列表
```
GET /api/sources
```

**功能**: 获取所有可用音源列表

**请求参数**:
| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `includeStatus` | boolean | ❌ | `false` | 是否包含状态信息 |

**响应示例**:
```json
{
  "状态码": 200,
  "消息": "音源列表获取成功",
  "时间戳": "2025-08-01T12:11:32.000Z",
  "数据": {
    "音源列表": [
      {
        "音源ID": "migu",
        "音源名称": "咪咕音乐",
        "已启用": true,
        "优先级": 1
      },
      {
        "音源ID": "kuwo",
        "音源名称": "酷我音乐",
        "已启用": true,
        "优先级": 2
      }
    ],
    "总数": 6,
    "已启用": 6
  }
}
```

### 2. 音源统计
```
GET /api/sources/stats
```

**功能**: 获取音源统计信息

**请求参数**: 无

**响应示例**:
```json
{
  "状态码": 200,
  "消息": "音源统计信息获取成功",
  "时间戳": "2025-08-01T12:11:32.000Z",
  "数据": {
    "音源总数": 6,
    "已启用音源": 6,
    "已禁用音源": 0,
    "音源详情": [
      {
        "音源ID": "migu",
        "音源名称": "咪咕音乐",
        "已启用": true,
        "优先级": 1
      }
    ],
    "最后更新": "2025-08-01T12:11:32.000Z"
  }
}
```

### 3. 音源配置
```
GET /api/sources/config
```

**功能**: 获取音源配置信息

**请求参数**: 无

**响应示例**:
```json
{
  "状态码": 200,
  "消息": "音源配置获取成功",
  "时间戳": "2025-08-01T12:11:32.000Z",
  "数据": {
    "已启用音源": ["migu", "kuwo", "qq", "kugou", "joox", "youtube"],
    "音源顺序": [
      {
        "音源ID": "migu",
        "音源名称": "咪咕音乐",
        "优先级": 1
      }
    ],
    "设置": {
      "遵循音源顺序": false,
      "启用无损": true,
      "最低音质": 0,
      "启用本地VIP": true
    }
  }
}
```

### 4. 单个音源详情
```
GET /api/sources/:sourceId
```

**功能**: 获取单个音源的详细信息

**路径参数**:
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `sourceId` | string | ✅ | 音源标识符 (migu/kuwo/qq/kugou/joox/youtube) |

**响应示例**:
```json
{
  "状态码": 200,
  "消息": "音源详情获取成功",
  "时间戳": "2025-08-01T12:11:32.000Z",
  "数据": {
    "音源ID": "migu",
    "音源名称": "咪咕音乐",
    "已启用": true,
    "优先级": 1,
    "功能特性": {
      "支持无损": true,
      "需要认证": false,
      "地区限制": false
    }
  }
}
```

---

## ⚠️ **错误处理**

### **统一错误响应格式**
```json
{
  "状态码": 400,
  "消息": "请求参数错误",
  "时间戳": "2025-08-01T12:11:32.000Z",
  "错误代码": "VALIDATION_ERROR",
  "详情": {
    "field": "songIds",
    "message": "歌曲ID是必需的"
  }
}
```

### **常见错误代码**
| 状态码 | 错误代码 | 说明 |
|--------|----------|------|
| 400 | VALIDATION_ERROR | 请求参数验证失败 |
| 404 | NOT_FOUND | 资源未找到 |
| 429 | RATE_LIMIT_EXCEEDED | 请求频率超限 |
| 500 | INTERNAL_ERROR | 服务器内部错误 |

---

## 🎯 **支持的音源**

| 音源ID | 音源名称 | 特点 | 音质支持 | 状态 |
|--------|----------|------|----------|------|
| `migu` | 咪咕音乐 | 主要音源，稳定性好 | 128k-999k | ✅ 可用 |
| `kuwo` | 酷我音乐 | 无损音质丰富 | 128k-FLAC | ✅ 可用 |
| `qq` | QQ音乐 | 流行歌曲全面 | 128k-999k | ✅ 可用 |
| `kugou` | 酷狗音乐 | 综合音源 | 128k-320k | ✅ 可用 |
| `joox` | JOOX | 国际音源 | 128k-320k | ✅ 可用 |
| `youtube` | YouTube | 备用音源 | 128k-320k | ✅ 可用 |

---

## 🚀 **性能优化特性**

### **并行处理优化**
- ✅ **sources模式**: 使用Promise.all()并行处理，性能提升5倍
- ✅ **批量操作**: 支持最多20首歌曲的并行处理
- ✅ **智能缓存**: 音源配置和统计信息智能缓存

### **参数优化**
- ✅ **精简参数**: 移除冗余的`includeSources`和`includeMetadata`参数
- ✅ **音质策略**: MIN_BR=0移除音质过滤，提高成功率
- ✅ **错误处理**: 完善的错误处理和超时机制

---

## 📝 **使用建议**

### **最佳实践**
1. **批量操作**: 对于多首歌曲，建议使用批量请求提高效率
2. **音源选择**: 建议优先使用migu和kuwo音源，成功率较高
3. **错误处理**: 建议实现重试机制，处理网络超时等临时错误
4. **频率控制**: 遵守API频率限制，避免被限流

### **测试工具**
- **HTML测试工具**: 使用项目根目录的`api-test-tool.html`进行可视化测试
- **命令行测试**: 使用curl命令进行快速API测试
- **集成测试**: 参考`tests/`目录下的测试用例

---

**API文档版本**: v1.0.0  
**最后更新**: 2025-08-01 20:11  
**文档状态**: ✅ 完整且已优化
