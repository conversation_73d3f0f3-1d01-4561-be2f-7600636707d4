#!/usr/bin/env node

/**
 * 配置验证工具
 * 用于验证硬编码优化后的配置文件正确性
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const levelColors = {
        INFO: colors.blue,
        SUCCESS: colors.green,
        WARNING: colors.yellow,
        ERROR: colors.red
    };
    
    const color = levelColors[level] || colors.reset;
    console.log(`${color}[${level}]${colors.reset} ${message}`);
    
    if (data) {
        console.log(JSON.stringify(data, null, 2));
    }
}

// 配置验证规则
const validationRules = {
    timeout: {
        unlock: { min: 5000, max: 120000, type: 'number' },
        sourceTest: { min: 3000, max: 60000, type: 'number' },
        apiRequest: { min: 5000, max: 120000, type: 'number' },
        healthCheck: { min: 1000, max: 30000, type: 'number' }
    },
    performance: {
        batchConcurrency: { min: 1, max: 20, type: 'number' }
    },
    limits: {
        maxBatchSize: { min: 1, max: 100, type: 'number' },
        maxSearchResults: { min: 1, max: 200, type: 'number' },
        maxKeywordLength: { min: 1, max: 500, type: 'number' },
        requestTimeout: { min: 5000, max: 120000, type: 'number' }
    },
    // 缓存配置已移除 - 无实际缓存实现
    // P2硬编码优化 - 新增配置验证规则
    logging: {
        level: { type: 'string', enum: ['error', 'warn', 'info', 'debug'] },
        fileEnabled: { type: 'boolean' },
        consoleEnabled: { type: 'boolean' },
        maxFiles: { type: 'string' },
        maxSize: { type: 'string' },
        datePattern: { type: 'string' },
        timeFormat: { type: 'string' }
    },
    testing: {
        defaultSongId: { type: 'string', minLength: 1 },
        defaultSongIds: { type: 'array', minItems: 1 },
        testKeywords: { type: 'array', minItems: 1 },
        testSources: { type: 'string', minLength: 1 },
        testArtists: { type: 'array', minItems: 1 },
        testAlbums: { type: 'array', minItems: 1 }
    },
    ui: {
        theme: {
            primaryColor: { type: 'string', pattern: /^#[0-9a-fA-F]{6}$/ },
            secondaryColor: { type: 'string', pattern: /^#[0-9a-fA-F]{6}$/ },
            accentColor: { type: 'string', pattern: /^#[0-9a-fA-F]{6}$/ },
            successColor: { type: 'string', pattern: /^#[0-9a-fA-F]{6}$/ },
            errorColor: { type: 'string', pattern: /^#[0-9a-fA-F]{6}$/ },
            warningColor: { type: 'string', pattern: /^#[0-9a-fA-F]{6}$/ }
        },
        layout: {
            maxWidth: { type: 'string', pattern: /^\d+px$/ },
            headerFontSize: { type: 'string', pattern: /^\d+(\.\d+)?em$/ },
            baseFontSize: { type: 'string', pattern: /^\d+(\.\d+)?em$/ }
        }
    }
};

// 验证单个配置项 (P2硬编码优化 - 扩展验证规则)
function validateConfigItem(value, rule, path) {
    const errors = [];

    // 类型检查
    if (rule.type === 'number' && typeof value !== 'number') {
        errors.push(`${path}: 期望数字类型，实际为 ${typeof value}`);
    } else if (rule.type === 'boolean' && typeof value !== 'boolean') {
        errors.push(`${path}: 期望布尔类型，实际为 ${typeof value}`);
    } else if (rule.type === 'string' && typeof value !== 'string') {
        errors.push(`${path}: 期望字符串类型，实际为 ${typeof value}`);
    } else if (rule.type === 'array' && !Array.isArray(value)) {
        errors.push(`${path}: 期望数组类型，实际为 ${typeof value}`);
    }

    // 数值范围检查
    if (rule.type === 'number' && typeof value === 'number') {
        if (rule.min !== undefined && value < rule.min) {
            errors.push(`${path}: 值 ${value} 小于最小值 ${rule.min}`);
        }
        if (rule.max !== undefined && value > rule.max) {
            errors.push(`${path}: 值 ${value} 大于最大值 ${rule.max}`);
        }
    }

    // 字符串长度检查
    if (rule.type === 'string' && typeof value === 'string') {
        if (rule.minLength !== undefined && value.length < rule.minLength) {
            errors.push(`${path}: 字符串长度 ${value.length} 小于最小长度 ${rule.minLength}`);
        }
        if (rule.maxLength !== undefined && value.length > rule.maxLength) {
            errors.push(`${path}: 字符串长度 ${value.length} 大于最大长度 ${rule.maxLength}`);
        }
    }

    // 数组长度检查
    if (rule.type === 'array' && Array.isArray(value)) {
        if (rule.minItems !== undefined && value.length < rule.minItems) {
            errors.push(`${path}: 数组长度 ${value.length} 小于最小长度 ${rule.minItems}`);
        }
        if (rule.maxItems !== undefined && value.length > rule.maxItems) {
            errors.push(`${path}: 数组长度 ${value.length} 大于最大长度 ${rule.maxItems}`);
        }
    }

    // 枚举值检查
    if (rule.enum && !rule.enum.includes(value)) {
        errors.push(`${path}: 值 "${value}" 不在允许的枚举值中: [${rule.enum.join(', ')}]`);
    }

    // 正则表达式检查
    if (rule.pattern && typeof value === 'string') {
        if (!rule.pattern.test(value)) {
            errors.push(`${path}: 值 "${value}" 不匹配要求的格式`);
        }
    }

    return errors;
}

// 递归验证配置对象
function validateConfigSection(config, rules, basePath = '') {
    const errors = [];
    
    for (const [key, rule] of Object.entries(rules)) {
        const currentPath = basePath ? `${basePath}.${key}` : key;
        const value = config[key];
        
        if (value === undefined) {
            errors.push(`${currentPath}: 配置项缺失`);
            continue;
        }
        
        if (typeof rule === 'object' && rule.type) {
            // 叶子节点验证
            errors.push(...validateConfigItem(value, rule, currentPath));
        } else if (typeof rule === 'object') {
            // 嵌套对象验证
            if (typeof value === 'object' && value !== null) {
                errors.push(...validateConfigSection(value, rule, currentPath));
            } else {
                errors.push(`${currentPath}: 期望对象类型，实际为 ${typeof value}`);
            }
        }
    }
    
    return errors;
}

// 检查环境变量
function validateEnvironmentVariables() {
    log('INFO', '检查环境变量配置...');
    
    const requiredEnvVars = [
        'UNLOCK_TIMEOUT',
        'SOURCE_TEST_TIMEOUT',
        'API_REQUEST_TIMEOUT',
        'HEALTH_CHECK_TIMEOUT',
        'BATCH_CONCURRENCY',
        'MAX_RETRIES',
        'RETRY_DELAY'
    ];

    // P2硬编码优化 - 新增环境变量
    const p2EnvVars = [
        'LOG_DATE_PATTERN',
        'LOG_TIME_FORMAT',
        'TEST_SONG_ID',
        'TEST_SONG_IDS',
        'TEST_KEYWORDS',
        'TEST_SOURCES',
        'TEST_ARTISTS',
        'TEST_ALBUMS',
        'UI_PRIMARY_COLOR',
        'UI_SECONDARY_COLOR',
        'UI_ACCENT_COLOR',
        'UI_SUCCESS_COLOR',
        'UI_ERROR_COLOR',
        'UI_WARNING_COLOR',
        'UI_MAX_WIDTH',
        'UI_HEADER_FONT_SIZE',
        'UI_BASE_FONT_SIZE'
    ];
    
    const errors = [];
    const warnings = [];
    
    // 检查.env文件是否存在
    const envPath = path.join(process.cwd(), '.env');
    if (!fs.existsSync(envPath)) {
        errors.push('.env 文件不存在');
        return { errors, warnings };
    }
    
    // 读取.env文件
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = {};
    
    envContent.split('\n').forEach(line => {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
            const [key, ...valueParts] = trimmed.split('=');
            if (key && valueParts.length > 0) {
                envVars[key.trim()] = valueParts.join('=').trim();
            }
        }
    });
    
    // 检查必需的环境变量
    for (const varName of requiredEnvVars) {
        if (!envVars[varName]) {
            errors.push(`环境变量 ${varName} 未设置`);
        } else {
            const value = envVars[varName];

            // 检查数值类型的环境变量
            if (varName.includes('TIMEOUT') || varName.includes('CONCURRENCY') || varName.includes('RETRIES') || varName.includes('DELAY')) {
                const numValue = parseInt(value);
                if (isNaN(numValue)) {
                    errors.push(`环境变量 ${varName} 应为数字，当前值: ${value}`);
                } else if (numValue <= 0) {
                    warnings.push(`环境变量 ${varName} 值为 ${numValue}，可能过小`);
                }
            }
        }
    }

    // 检查P2阶段的环境变量 (可选但建议设置)
    for (const varName of p2EnvVars) {
        if (!envVars[varName]) {
            warnings.push(`P2优化环境变量 ${varName} 未设置，将使用默认值`);
        } else {
            const value = envVars[varName];

            // 检查颜色格式
            if (varName.includes('COLOR')) {
                if (!/^#[0-9a-fA-F]{6}$/.test(value)) {
                    errors.push(`环境变量 ${varName} 颜色格式错误，应为 #RRGGBB 格式，当前值: ${value}`);
                }
            }

            // 检查尺寸格式
            if (varName.includes('WIDTH') && !/^\d+px$/.test(value)) {
                errors.push(`环境变量 ${varName} 宽度格式错误，应为数字+px格式，当前值: ${value}`);
            }

            if (varName.includes('FONT_SIZE') && !/^\d+(\.\d+)?em$/.test(value)) {
                errors.push(`环境变量 ${varName} 字体大小格式错误，应为数字+em格式，当前值: ${value}`);
            }
        }
    }
    
    return { errors, warnings };
}

// 验证配置文件语法
function validateConfigSyntax() {
    log('INFO', '检查配置文件语法...');
    
    const configPath = path.join(process.cwd(), 'src/config/config.js');
    
    if (!fs.existsSync(configPath)) {
        return ['配置文件不存在: src/config/config.js'];
    }
    
    try {
        // 尝试加载配置文件
        delete require.cache[require.resolve(configPath)];
        const config = require(configPath);
        
        if (!config || typeof config !== 'object') {
            return ['配置文件未导出有效的配置对象'];
        }
        
        log('SUCCESS', '配置文件语法正确');
        return [];
    } catch (error) {
        return [`配置文件语法错误: ${error.message}`];
    }
}

// 验证配置完整性
function validateConfigCompleteness() {
    log('INFO', '检查配置完整性...');
    
    try {
        const configPath = path.join(process.cwd(), 'src/config/config.js');
        delete require.cache[require.resolve(configPath)];
        const config = require(configPath);
        
        const errors = validateConfigSection(config, validationRules);
        
        if (errors.length === 0) {
            log('SUCCESS', '配置完整性检查通过');
        }
        
        return errors;
    } catch (error) {
        return [`配置加载失败: ${error.message}`];
    }
}

// 性能建议分析
function analyzePerformanceSettings() {
    log('INFO', '分析性能配置...');
    
    try {
        const configPath = path.join(process.cwd(), 'src/config/config.js');
        delete require.cache[require.resolve(configPath)];
        const config = require(configPath);
        
        const suggestions = [];
        
        // 超时配置建议
        if (config.timeout) {
            if (config.timeout.unlock > 45000) {
                suggestions.push('解锁超时时间过长，可能影响用户体验');
            }
            if (config.timeout.sourceTest > 15000) {
                suggestions.push('音源测试超时时间过长，建议调整为10秒以内');
            }
        }
        
        // 并发配置建议
        if (config.performance) {
            if (config.performance.batchConcurrency > 10) {
                suggestions.push('批量并发数较高，请确保服务器资源充足');
            }
            if (config.performance.batchConcurrency < 3) {
                suggestions.push('批量并发数较低，可能影响处理效率');
            }
        }
        
        // API限制配置建议
        if (config.limits) {
            if (config.limits.maxBatchSize > 50) {
                suggestions.push('批量操作数量较大，可能影响服务器性能');
            }
            if (config.limits.maxSearchResults > 100) {
                suggestions.push('搜索结果数量较大，可能影响响应速度');
            }
            if (config.limits.requestTimeout < 10000) {
                suggestions.push('请求超时时间较短，可能导致请求失败率增加');
            }
        }

        // 缓存配置建议已移除 - 无实际缓存实现
        
        return suggestions;
    } catch (error) {
        return [`性能分析失败: ${error.message}`];
    }
}

// 生成验证报告
function generateValidationReport(results) {
    const timestamp = new Date().toISOString();
    const reportPath = `config-validation-report-${timestamp.replace(/[:.]/g, '-')}.md`;
    
    let report = `# 配置验证报告\n\n`;
    report += `**生成时间**: ${timestamp}\n\n`;
    
    // 总体状态
    const totalErrors = results.syntaxErrors.length + results.completenessErrors.length + results.envErrors.length;
    const totalWarnings = results.envWarnings.length + results.performanceSuggestions.length;
    
    if (totalErrors === 0) {
        report += `## ✅ 验证状态: 通过\n\n`;
    } else {
        report += `## ❌ 验证状态: 失败 (${totalErrors} 个错误)\n\n`;
    }
    
    // 错误详情
    if (totalErrors > 0) {
        report += `## 🚨 错误详情\n\n`;
        
        if (results.syntaxErrors.length > 0) {
            report += `### 语法错误\n`;
            results.syntaxErrors.forEach(error => {
                report += `- ${error}\n`;
            });
            report += `\n`;
        }
        
        if (results.completenessErrors.length > 0) {
            report += `### 完整性错误\n`;
            results.completenessErrors.forEach(error => {
                report += `- ${error}\n`;
            });
            report += `\n`;
        }
        
        if (results.envErrors.length > 0) {
            report += `### 环境变量错误\n`;
            results.envErrors.forEach(error => {
                report += `- ${error}\n`;
            });
            report += `\n`;
        }
    }
    
    // 警告和建议
    if (totalWarnings > 0) {
        report += `## ⚠️ 警告和建议\n\n`;
        
        if (results.envWarnings.length > 0) {
            report += `### 环境变量警告\n`;
            results.envWarnings.forEach(warning => {
                report += `- ${warning}\n`;
            });
            report += `\n`;
        }
        
        if (results.performanceSuggestions.length > 0) {
            report += `### 性能优化建议\n`;
            results.performanceSuggestions.forEach(suggestion => {
                report += `- ${suggestion}\n`;
            });
            report += `\n`;
        }
    }
    
    // 配置摘要
    try {
        const configPath = path.join(process.cwd(), 'src/config/config.js');
        delete require.cache[require.resolve(configPath)];
        const config = require(configPath);
        
        report += `## 📊 配置摘要\n\n`;
        
        if (config.timeout) {
            report += `### 超时配置\n`;
            Object.entries(config.timeout).forEach(([key, value]) => {
                report += `- ${key}: ${value}ms\n`;
            });
            report += `\n`;
        }
        
        if (config.performance) {
            report += `### 性能配置\n`;
            Object.entries(config.performance).forEach(([key, value]) => {
                report += `- ${key}: ${value}\n`;
            });
            report += `\n`;
        }
    } catch (error) {
        report += `配置摘要生成失败: ${error.message}\n\n`;
    }
    
    fs.writeFileSync(reportPath, report);
    log('SUCCESS', `验证报告已生成: ${reportPath}`);
}

// 主函数
function main() {
    console.log(`${colors.cyan}=== 配置验证工具 ===${colors.reset}`);
    console.log('检查硬编码优化后的配置文件正确性\n');
    
    const results = {
        syntaxErrors: [],
        completenessErrors: [],
        envErrors: [],
        envWarnings: [],
        performanceSuggestions: []
    };
    
    // 1. 语法检查
    results.syntaxErrors = validateConfigSyntax();
    
    // 2. 环境变量检查
    const envValidation = validateEnvironmentVariables();
    results.envErrors = envValidation.errors;
    results.envWarnings = envValidation.warnings;
    
    // 3. 配置完整性检查
    if (results.syntaxErrors.length === 0) {
        results.completenessErrors = validateConfigCompleteness();
        results.performanceSuggestions = analyzePerformanceSettings();
    }
    
    // 输出结果
    const totalErrors = results.syntaxErrors.length + results.completenessErrors.length + results.envErrors.length;
    const totalWarnings = results.envWarnings.length + results.performanceSuggestions.length;
    
    console.log(`\n${colors.cyan}=== 验证结果 ===${colors.reset}`);
    
    if (totalErrors === 0) {
        log('SUCCESS', `配置验证通过！`);
    } else {
        log('ERROR', `发现 ${totalErrors} 个错误`);
        
        // 显示错误详情
        [...results.syntaxErrors, ...results.completenessErrors, ...results.envErrors].forEach(error => {
            log('ERROR', error);
        });
    }
    
    if (totalWarnings > 0) {
        log('WARNING', `发现 ${totalWarnings} 个警告或建议`);
        
        // 显示警告详情
        [...results.envWarnings, ...results.performanceSuggestions].forEach(warning => {
            log('WARNING', warning);
        });
    }
    
    // 生成详细报告
    generateValidationReport(results);
    
    // 退出码
    process.exit(totalErrors > 0 ? 1 : 0);
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = {
    validateConfigSection,
    validateEnvironmentVariables,
    validateConfigSyntax,
    validateConfigCompleteness,
    analyzePerformanceSettings
};
