{"title": "FOLLOW_SOURCE_ORDER 配置行为验证报告", "timestamp": "2025-08-02T02:35:26.682Z", "testConfig": {"songId": "418602084", "sources": "migu,kuwo,qq,kugou", "rounds": 5}, "results": {"parallel": [{"round": 1, "success": true, "duration": 883, "source": "migu", "sourceName": "咪咕音乐", "bitrate": 128000, "quality": "标准", "fileSize": 4261973, "format": "mp3", "priority": 1}, {"round": 2, "success": true, "duration": 158, "source": "migu", "sourceName": "咪咕音乐", "bitrate": 128000, "quality": "标准", "fileSize": 4261973, "format": "mp3", "priority": 1}, {"round": 3, "success": true, "duration": 564, "source": "migu", "sourceName": "咪咕音乐", "bitrate": 128000, "quality": "标准", "fileSize": 4261973, "format": "mp3", "priority": 1}, {"round": 4, "success": true, "duration": 182, "source": "migu", "sourceName": "咪咕音乐", "bitrate": 128000, "quality": "标准", "fileSize": 4261973, "format": "mp3", "priority": 1}, {"round": 5, "success": true, "duration": 176, "source": "migu", "sourceName": "咪咕音乐", "bitrate": 128000, "quality": "标准", "fileSize": 4261973, "format": "mp3", "priority": 1}], "sequential": [{"round": 1, "success": true, "duration": 663, "source": "migu", "sourceName": "咪咕音乐", "bitrate": 128000, "quality": "标准", "fileSize": 4261973, "format": "mp3", "priority": 1}, {"round": 2, "success": true, "duration": 165, "source": "migu", "sourceName": "咪咕音乐", "bitrate": 128000, "quality": "标准", "fileSize": 4261973, "format": "mp3", "priority": 1}, {"round": 3, "success": true, "duration": 143, "source": "migu", "sourceName": "咪咕音乐", "bitrate": 128000, "quality": "标准", "fileSize": 4261973, "format": "mp3", "priority": 1}, {"round": 4, "success": true, "duration": 132, "source": "migu", "sourceName": "咪咕音乐", "bitrate": 128000, "quality": "标准", "fileSize": 4261973, "format": "mp3", "priority": 1}, {"round": 5, "success": true, "duration": 133, "source": "migu", "sourceName": "咪咕音乐", "bitrate": 128000, "quality": "标准", "fileSize": 4261973, "format": "mp3", "priority": 1}]}, "analysis": {"parallel": {"successRate": 1, "avgDuration": 392.6, "sourceCounts": {"migu": 5}, "avgBitrate": 128000}, "sequential": {"successRate": 1, "avgDuration": 247.2, "sourceCounts": {"migu": 5}, "avgBitrate": 128000}}, "conclusions": ["顺序模式 (FOLLOW_SOURCE_ORDER=true) 使用固定音源，音源选择可预测", "两种模式的平均音质相近，没有明显的音质优先选择差异"]}