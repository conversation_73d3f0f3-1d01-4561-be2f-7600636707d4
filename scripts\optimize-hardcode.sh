#!/bin/bash

# 硬编码优化实施脚本
# 基于 HARDCODE-OPTIMIZATION-PLAN.md 的自动化实施工具

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# 检查前置条件
check_prerequisites() {
    log_header "检查前置条件"
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    log_success "Node.js 已安装: $(node --version)"
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    log_success "npm 已安装: $(npm --version)"
    
    # 检查项目文件
    REQUIRED_FILES=("package.json" "src/app.js" "src/config/config.js" ".env")
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "必需文件不存在: $file"
            exit 1
        fi
    done
    log_success "项目文件检查通过"
}

# 创建备份
create_backup() {
    log_header "创建配置备份"
    
    BACKUP_DIR="backup/hardcode-optimization-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份关键文件
    BACKUP_FILES=(
        "src/config/config.js"
        "src/services/unlockService.js"
        "api-test-tool.html"
        ".env"
        "src/utils/constants.js"
    )
    
    for file in "${BACKUP_FILES[@]}"; do
        if [ -f "$file" ]; then
            cp "$file" "$BACKUP_DIR/"
            log_success "已备份: $file"
        fi
    done
    
    echo "$BACKUP_DIR" > .backup_location
    log_success "备份完成，位置: $BACKUP_DIR"
}

# P0阶段: 网络超时配置化
optimize_p0_timeouts() {
    log_header "P0优化: 网络超时配置化"
    
    # 1. 扩展config.js - 添加timeout配置
    log_info "扩展配置文件..."
    
    # 检查是否已经存在timeout配置
    if grep -q "timeout:" src/config/config.js; then
        log_warning "timeout配置已存在，跳过"
    else
        # 在config对象中添加timeout配置
        sed -i '/server: {/a\
\
    // 网络超时配置\
    timeout: {\
        unlock: parseInt(process.env.UNLOCK_TIMEOUT) || 30000,\
        sourceTest: parseInt(process.env.SOURCE_TEST_TIMEOUT) || 10000,\
        apiRequest: parseInt(process.env.API_REQUEST_TIMEOUT) || 30000,\
        healthCheck: parseInt(process.env.HEALTH_CHECK_TIMEOUT) || 5000\
    },' src/config/config.js
        log_success "已添加timeout配置到config.js"
    fi
    
    # 2. 更新.env文件
    log_info "更新环境变量..."
    
    ENV_VARS=(
        "UNLOCK_TIMEOUT=30000"
        "SOURCE_TEST_TIMEOUT=10000"
        "API_REQUEST_TIMEOUT=30000"
        "HEALTH_CHECK_TIMEOUT=5000"
    )
    
    for var in "${ENV_VARS[@]}"; do
        var_name=$(echo "$var" | cut -d'=' -f1)
        if grep -q "^$var_name=" .env; then
            log_warning "$var_name 已存在于.env中"
        else
            echo "" >> .env
            echo "# 网络超时配置 (硬编码优化)" >> .env
            echo "$var" >> .env
            log_success "已添加 $var 到.env"
        fi
    done
}

# P0阶段: 并发控制配置化
optimize_p0_concurrency() {
    log_header "P0优化: 并发控制配置化"
    
    # 1. 添加performance配置
    if grep -q "performance:" src/config/config.js; then
        log_warning "performance配置已存在，跳过"
    else
        sed -i '/timeout: {/,/},/a\
\
    // 性能配置\
    performance: {\
        batchConcurrency: parseInt(process.env.BATCH_CONCURRENCY) || 5,\
        maxRetries: parseInt(process.env.MAX_RETRIES) || 3,\
        retryDelay: parseInt(process.env.RETRY_DELAY) || 1000\
    },' src/config/config.js
        log_success "已添加performance配置到config.js"
    fi
    
    # 2. 更新.env文件
    PERF_VARS=(
        "BATCH_CONCURRENCY=5"
        "MAX_RETRIES=3"
        "RETRY_DELAY=1000"
    )
    
    for var in "${PERF_VARS[@]}"; do
        var_name=$(echo "$var" | cut -d'=' -f1)
        if ! grep -q "^$var_name=" .env; then
            echo "" >> .env
            echo "# 性能配置 (硬编码优化)" >> .env
            echo "$var" >> .env
            log_success "已添加 $var 到.env"
        fi
    done
}

# 验证配置
validate_configuration() {
    log_header "验证配置"
    
    # 1. 语法检查
    log_info "检查配置文件语法..."
    if node -c src/config/config.js; then
        log_success "config.js 语法正确"
    else
        log_error "config.js 语法错误"
        return 1
    fi
    
    # 2. 加载测试
    log_info "测试配置加载..."
    if node -e "const config = require('./src/config/config'); console.log('配置加载成功')"; then
        log_success "配置加载测试通过"
    else
        log_error "配置加载失败"
        return 1
    fi
    
    # 3. 环境变量验证
    log_info "验证环境变量..."
    source .env
    
    REQUIRED_VARS=("UNLOCK_TIMEOUT" "SOURCE_TEST_TIMEOUT" "BATCH_CONCURRENCY")
    for var in "${REQUIRED_VARS[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "环境变量 $var 未设置"
            return 1
        else
            log_success "$var = ${!var}"
        fi
    done
}

# 运行测试
run_tests() {
    log_header "运行测试验证"
    
    # 1. 安装依赖
    log_info "安装依赖..."
    npm install
    
    # 2. 运行语法检查
    log_info "运行ESLint检查..."
    if npm run lint; then
        log_success "代码语法检查通过"
    else
        log_warning "代码语法检查发现问题，但不影响功能"
    fi
    
    # 3. 运行单元测试
    if [ -f "package.json" ] && grep -q '"test"' package.json; then
        log_info "运行单元测试..."
        if npm test; then
            log_success "单元测试通过"
        else
            log_warning "单元测试失败，请检查"
        fi
    fi
    
    # 4. 启动服务测试
    log_info "测试服务启动..."
    timeout 10s npm start &
    SERVER_PID=$!
    sleep 5
    
    if kill -0 $SERVER_PID 2>/dev/null; then
        log_success "服务启动成功"
        kill $SERVER_PID
        wait $SERVER_PID 2>/dev/null
    else
        log_error "服务启动失败"
        return 1
    fi
}

# 回滚函数
rollback() {
    log_header "执行回滚操作"
    
    if [ -f ".backup_location" ]; then
        BACKUP_DIR=$(cat .backup_location)
        if [ -d "$BACKUP_DIR" ]; then
            log_info "从备份恢复文件: $BACKUP_DIR"
            
            # 恢复备份文件
            for file in "$BACKUP_DIR"/*; do
                if [ -f "$file" ]; then
                    filename=$(basename "$file")
                    if [ "$filename" = "config.js" ]; then
                        cp "$file" "src/config/config.js"
                    elif [ "$filename" = "unlockService.js" ]; then
                        cp "$file" "src/services/unlockService.js"
                    else
                        cp "$file" "./"
                    fi
                    log_success "已恢复: $filename"
                fi
            done
            
            log_success "回滚完成"
        else
            log_error "备份目录不存在: $BACKUP_DIR"
        fi
    else
        log_error "未找到备份位置信息"
    fi
}

# 生成优化报告
generate_report() {
    log_header "生成优化报告"
    
    REPORT_FILE="hardcode-optimization-report-$(date +%Y%m%d-%H%M%S).md"
    
    cat > "$REPORT_FILE" << EOF
# 硬编码优化实施报告

## 优化概览
- **执行时间**: $(date)
- **优化阶段**: P0 (高风险硬编码)
- **处理项目**: 网络超时配置化、并发控制配置化

## 配置变更
### 新增环境变量
- UNLOCK_TIMEOUT=30000
- SOURCE_TEST_TIMEOUT=10000
- API_REQUEST_TIMEOUT=30000
- HEALTH_CHECK_TIMEOUT=5000
- BATCH_CONCURRENCY=5
- MAX_RETRIES=3
- RETRY_DELAY=1000

### 修改文件
- src/config/config.js (添加timeout和performance配置)
- .env (添加新的环境变量)

## 验证结果
- ✅ 配置文件语法正确
- ✅ 配置加载成功
- ✅ 环境变量设置正确
- ✅ 服务启动正常

## 下一步
- 继续P1阶段优化 (API限制配置化)
- 更新业务逻辑使用新配置
- 完善测试用例

## 回滚信息
- 备份位置: $(cat .backup_location 2>/dev/null || echo "未找到")
- 回滚命令: ./scripts/optimize-hardcode.sh --rollback
EOF

    log_success "优化报告已生成: $REPORT_FILE"
}

# 主函数
main() {
    log_header "硬编码优化实施工具"
    log_info "基于 HARDCODE-OPTIMIZATION-PLAN.md 的自动化实施"
    
    # 处理命令行参数
    case "${1:-}" in
        --rollback)
            rollback
            exit 0
            ;;
        --help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --rollback  回滚到优化前状态"
            echo "  --help      显示帮助信息"
            exit 0
            ;;
    esac
    
    # 执行优化流程
    check_prerequisites
    create_backup
    
    # P0阶段优化
    optimize_p0_timeouts
    optimize_p0_concurrency
    
    # 验证和测试
    if validate_configuration && run_tests; then
        generate_report
        log_success "P0阶段硬编码优化完成！"
        log_info "下一步: 手动更新业务逻辑使用新配置"
        log_info "参考: HARDCODE-OPTIMIZATION-PLAN.md 中的P1阶段"
    else
        log_error "优化过程中发现问题，建议执行回滚"
        log_info "回滚命令: $0 --rollback"
        exit 1
    fi
}

# 错误处理
trap 'log_error "脚本执行失败，可能需要手动回滚"; exit 1' ERR

# 执行主函数
main "$@"
