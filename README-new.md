# 🎵 音乐解锁服务后端

[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![License](https://img.shields.io/badge/License-MIT-blue.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)]()

基于 UnblockNeteaseMusic 的高性能音乐解锁服务后端，提供完整的 RESTful API 接口和多音源并行处理能力。

## ✨ 功能特性

### 🎵 核心功能
- **多音源支持**: 集成咪咕、酷我、QQ音乐、酷狗、JOOX、YouTube等主流音源
- **智能解锁**: 支持单首和批量解锁，自动选择最优音源
- **高音质支持**: 支持无损FLAC、320kbps MP3等多种音质
- **并行处理**: 多音源并行查询，显著提升响应速度

### 🔍 搜索功能
- **精确搜索**: 支持歌曲ID精确匹配
- **模糊搜索**: 歌曲名、歌手名智能匹配
- **批量处理**: 支持多关键词批量搜索
- **结果优化**: 可配置搜索结果数量和质量

### 🛡️ 安全特性
- **频率限制**: 可配置的API调用频率控制
- **CORS保护**: 灵活的跨域访问控制
- **请求验证**: 完整的参数验证和错误处理
- **会话安全**: 安全的会话管理和密钥保护

### 📊 监控和日志
- **结构化日志**: 基于Winston的分级日志系统
- **性能监控**: 内置响应时间和成功率统计
- **健康检查**: 完善的服务状态监控接口
- **错误追踪**: 详细的错误日志和调试信息

## 🚀 快速开始

### 环境要求

- **Node.js**: 18.0+ (推荐 18.17.0 LTS)
- **npm**: 9.0+ 或 yarn 1.22+
- **内存**: 最小 512MB，推荐 1GB+
- **网络**: 稳定的互联网连接

### 安装和运行

```bash
# 1. 克隆项目
git clone <repository-url>
cd music-unlock-server

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置必要的配置项

# 4. 验证配置
npm run validate-config

# 5. 启动开发服务
npm run dev

# 6. 验证服务
npm run test
```

### 服务验证

启动成功后，访问以下地址验证服务：

- **服务首页**: http://localhost:50091
- **API文档**: http://localhost:50091/music  
- **健康检查**: http://localhost:50091/music/source

## ⚙️ 配置说明

### 核心配置

```bash
# 服务基础配置
PORT=50091                   # 服务端口
HOST=localhost              # 服务主机  
NODE_ENV=development        # 环境模式

# 超时控制配置
UNLOCK_TIMEOUT=30000        # 解锁超时时间(ms)
SOURCE_TEST_TIMEOUT=10000   # 音源测试超时(ms)
REQUEST_TIMEOUT=30000       # 请求超时时间(ms)

# 性能控制配置
BATCH_CONCURRENCY=5         # 批量处理并发数
```

### 安全配置

```bash
# 频率限制
RATE_LIMIT_MAX_REQUESTS=100 # 最大请求数
RATE_LIMIT_WINDOW_MS=900000 # 时间窗口(ms)

# CORS和安全
CORS_ORIGIN=*               # 允许的源地址
SESSION_SECRET=change-this-in-production  # 会话密钥
MAX_REQUEST_SIZE=10mb       # 最大请求大小
```

### 音乐服务配置

```bash
# 音源配置
MUSIC_SOURCES=migu,kuwo,qq,kugou,joox,youtube  # 启用的音源
ENABLE_FLAC=true            # 启用无损音质
ENABLE_LYRIC=true           # 启用歌词获取
FOLLOW_SOURCE_ORDER=false   # 是否按音源顺序解锁
BLOCK_ADS=true              # 屏蔽广告音频

# 音源认证（可选）
NETEASE_COOKIE=             # 网易云Cookie
QQ_COOKIE=                  # QQ音乐Cookie
MIGU_COOKIE=                # 咪咕音乐Cookie
YOUTUBE_KEY=                # YouTube API密钥
```

## 📡 API 接口

### 音乐解锁

#### 单首歌曲解锁
```http
GET /music/unlock?sources=qq,migu&songs=418602084
```

#### 批量歌曲解锁
```http
GET /music/unlock?sources=qq,migu,kuwo&songs=418602084,123456,186016
```

#### 响应格式
```json
{
  "状态码": 200,
  "消息": "单首歌曲解锁完成",
  "时间戳": "2025-08-02T01:54:25.609Z",
  "解锁总数": 1,
  "解锁成功": 1,
  "解锁失败": 0,
  "解锁成功率": "100.0%",
  "成功列表": {
    "歌曲ID": 418602084,
    "播放链接": "http://...",
    "音源ID": "migu",
    "音源名称": "咪咕音乐",
    "音质": 128000,
    "音质描述": "标准",
    "文件大小": 3456789,
    "格式": "mp3",
    "优先级": 2,
    "解锁时间": "2025-08-02T01:54:25.609Z"
  },
  "失败列表": []
}
```

### 音源管理

#### 获取音源状态
```http
GET /music/source
```

#### 响应格式
```json
{
  "状态码": 200,
  "消息": "音源管理信息",
  "时间戳": "2025-08-02T01:54:25.609Z",
  "音源配置": {
    "启用音源": ["migu", "kuwo", "qq", "kugou", "joox", "youtube"],
    "音源总数": 6,
    "并行模式": true,
    "无损支持": true
  }
}
```

## 🏗️ 项目结构

```
music-unlock-server/
├── src/                    # 源代码目录
│   ├── app.js             # 应用入口文件
│   ├── config/            # 配置管理
│   │   └── config.js      # 主配置文件
│   ├── controllers/       # 控制器层
│   │   └── sourceController.js
│   ├── middleware/        # 中间件
│   │   ├── errorHandler.js   # 错误处理
│   │   ├── logger.js         # 日志中间件
│   │   └── validator.js      # 参数验证
│   ├── routes/            # 路由层
│   │   ├── musicRoutes.js    # 音乐API路由
│   │   └── sourceRoutes.js   # 音源管理路由
│   ├── services/          # 业务逻辑层
│   │   ├── musicService.js   # 音乐服务
│   │   └── unlockService.js  # 解锁服务
│   └── utils/             # 工具函数
│       ├── constants.js      # 常量定义
│       └── response.js       # 响应格式化
├── scripts/               # 脚本工具
│   └── validate-config.js    # 配置验证脚本
├── tests/                 # 测试文件
├── logs/                  # 日志目录
├── docs/                  # 文档目录
├── .env.example          # 环境变量模板
├── package.json          # 项目配置
└── README.md            # 项目说明
```

## 🧪 开发和测试

### 开发模式

```bash
# 启动开发服务（自动重启）
npm run dev

# 配置验证
npm run validate-config

# 运行测试套件
npm test

# 快速验证服务
node final-verification.js
```

### 测试工具

项目提供了多个测试脚本：

- **test-runner.js**: 完整的自动化测试套件
- **simple-test.js**: 简化版快速测试
- **final-verification.js**: 最终验证脚本

```bash
# 运行完整测试
node test-runner.js

# 快速测试
node simple-test.js

# 最终验证
node final-verification.js
```

## 🚀 部署指南

### Docker 部署

```bash
# 构建镜像
docker build -t music-unlock-server:latest .

# 运行容器
docker run -d \
  --name music-unlock-server \
  --restart unless-stopped \
  -p 50091:50091 \
  -v $(pwd)/.env:/app/.env:ro \
  -v $(pwd)/logs:/app/logs \
  music-unlock-server:latest
```

### PM2 部署

```bash
# 安装 PM2
npm install -g pm2

# 启动服务
pm2 start src/app.js --name music-unlock-server

# 查看状态
pm2 status

# 查看日志
pm2 logs music-unlock-server

# 重启服务
pm2 restart music-unlock-server
```

### 生产环境配置

```bash
# 设置生产环境变量
NODE_ENV=production
PORT=50091
HOST=0.0.0.0

# 安全配置
CORS_ORIGIN=https://yourdomain.com
SESSION_SECRET=your-super-secure-random-string
MAX_REQUEST_SIZE=10mb

# 性能优化
BATCH_CONCURRENCY=3
REQUEST_TIMEOUT=30000
UNLOCK_TIMEOUT=25000

# 日志配置
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=false
```

## 📊 性能指标

### 响应性能
| 操作类型 | 响应时间 | 并发能力 | 优化特性 |
|----------|----------|----------|----------|
| 单首解锁 | < 3秒 | 100+ req/min | 智能音源选择 |
| 批量解锁 | < 10秒 (20首) | 50+ req/min | 并行处理 |
| 音源分析 | < 5秒 | 80+ req/min | 并行优化 |
| 状态检查 | < 2秒 | 150+ req/min | 轻量级检查 |

### 系统资源
- **内存占用**: < 100MB (正常运行)
- **CPU使用**: < 10% (空闲时)
- **磁盘空间**: 日志文件自动轮转，最多保留14天
- **网络带宽**: 根据音乐文件大小动态调整

## 📚 文档

- **项目总结文档.md**: 完整的项目技术总结
- **配置优化完成报告.md**: 配置优化详细报告
- **部署和维护指南.md**: 生产环境部署指南
- **配置审查报告.md**: 配置分析结果
- **配置迁移指南.md**: 配置变更指导

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- [UnblockNeteaseMusic](https://github.com/UnblockNeteaseMusic/server) - 核心解锁引擎
- [Express.js](https://expressjs.com/) - Web框架
- [Winston](https://github.com/winstonjs/winston) - 日志系统
