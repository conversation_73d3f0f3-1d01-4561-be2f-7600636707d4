/**
 * 配置效果验证测试脚本
 * 测试 ENABLE_LOCAL_VIP、BLOCK_ADS、FOLLOW_SOURCE_ORDER 配置的实际效果
 */

const axios = require('axios');
const fs = require('fs');

const BASE_URL = 'http://localhost:50091';
const TEST_SONG_ID = '418602084'; // 周杰伦 - 稻香

// 测试配置
const configs = [
    {
        name: 'ENABLE_LOCAL_VIP=true, FOLLOW_SOURCE_ORDER=false, BLOCK_ADS=true',
        env: {
            ENABLE_LOCAL_VIP: 'true',
            FOLLOW_SOURCE_ORDER: 'false',
            BLOCK_ADS: 'true'
        }
    },
    {
        name: 'ENABLE_LOCAL_VIP=false, FOLLOW_SOURCE_ORDER=false, BLOCK_ADS=true',
        env: {
            ENABLE_LOCAL_VIP: 'false',
            FOLLOW_SOURCE_ORDER: 'false',
            BLOCK_ADS: 'true'
        }
    },
    {
        name: 'ENABLE_LOCAL_VIP=true, FOLLOW_SOURCE_ORDER=true, BLOCK_ADS=true',
        env: {
            ENABLE_LOCAL_VIP: 'true',
            FOLLOW_SOURCE_ORDER: 'true',
            BLOCK_ADS: 'true'
        }
    },
    {
        name: 'ENABLE_LOCAL_VIP=true, FOLLOW_SOURCE_ORDER=false, BLOCK_ADS=false',
        env: {
            ENABLE_LOCAL_VIP: 'true',
            FOLLOW_SOURCE_ORDER: 'false',
            BLOCK_ADS: 'false'
        }
    }
];

/**
 * 修改 .env 文件中的配置
 */
function updateEnvFile(envVars) {
    let envContent = fs.readFileSync('.env', 'utf8');
    
    for (const [key, value] of Object.entries(envVars)) {
        const regex = new RegExp(`^${key}=.*$`, 'm');
        if (envContent.match(regex)) {
            envContent = envContent.replace(regex, `${key}=${value}`);
        } else {
            envContent += `\n${key}=${value}`;
        }
    }
    
    fs.writeFileSync('.env', envContent);
    console.log(`✅ 已更新配置: ${JSON.stringify(envVars)}`);
}

/**
 * 等待服务重启
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 检查服务是否可用
 */
async function waitForService(maxRetries = 30) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            const response = await axios.get(`${BASE_URL}/`, { timeout: 5000 });
            if (response.status === 200) {
                console.log('✅ 服务已就绪');
                return true;
            }
        } catch (error) {
            console.log(`⏳ 等待服务启动... (${i + 1}/${maxRetries})`);
            await sleep(2000);
        }
    }
    throw new Error('服务启动超时');
}

/**
 * 测试音乐解锁功能
 */
async function testUnlock(sources = 'migu,kuwo,qq') {
    try {
        const response = await axios.get(`${BASE_URL}/music/unlock`, {
            params: {
                sources: sources,
                songs: TEST_SONG_ID
            },
            timeout: 30000
        });
        
        if (response.data && response.data.成功列表) {
            const result = response.data.成功列表;
            return {
                success: true,
                source: result.音源ID,
                bitrate: result.音质,
                quality: result.音质描述,
                fileSize: result.文件大小,
                url: result.播放链接 ? '有效' : '无效'
            };
        } else {
            return { success: false, error: '解锁失败' };
        }
    } catch (error) {
        return { success: false, error: error.message };
    }
}

/**
 * 获取音源配置信息
 */
async function getSourceConfig() {
    try {
        const response = await axios.get(`${BASE_URL}/music/source`, { timeout: 10000 });
        return {
            success: true,
            enableLocalVip: response.data.启用本地VIP,
            followSourceOrder: response.data.遵循音源顺序,
            enableFlac: response.data.启用无损,
            sources: response.data.音源优先级顺序.slice(0, 3).map(s => ({
                id: s.音源ID,
                name: s.音源名称,
                priority: s.优先级
            }))
        };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

/**
 * 运行单个配置测试
 */
async function runConfigTest(config) {
    console.log(`\n🧪 测试配置: ${config.name}`);
    console.log('=' .repeat(80));
    
    // 更新配置
    updateEnvFile(config.env);
    
    // 等待配置生效（nodemon会自动重启）
    console.log('⏳ 等待服务重启...');
    await sleep(5000);
    
    // 等待服务可用
    await waitForService();
    
    // 获取当前配置
    const sourceConfig = await getSourceConfig();
    if (sourceConfig.success) {
        console.log('📋 当前配置状态:');
        console.log(`   启用本地VIP: ${sourceConfig.enableLocalVip}`);
        console.log(`   遵循音源顺序: ${sourceConfig.followSourceOrder}`);
        console.log(`   启用无损: ${sourceConfig.enableFlac}`);
        console.log(`   音源优先级: ${sourceConfig.sources.map(s => `${s.id}(${s.priority})`).join(', ')}`);
    }
    
    // 测试解锁功能
    console.log('\n🎵 测试解锁功能:');
    
    // 测试1: 使用前3个音源
    const test1 = await testUnlock('migu,kuwo,qq');
    console.log(`   测试1 (migu,kuwo,qq): ${test1.success ? '✅' : '❌'}`);
    if (test1.success) {
        console.log(`     音源: ${test1.source}, 音质: ${test1.bitrate}bps (${test1.quality}), 大小: ${test1.fileSize}字节`);
    } else {
        console.log(`     错误: ${test1.error}`);
    }
    
    // 测试2: 只使用单个音源
    const test2 = await testUnlock('migu');
    console.log(`   测试2 (仅migu): ${test2.success ? '✅' : '❌'}`);
    if (test2.success) {
        console.log(`     音源: ${test2.source}, 音质: ${test2.bitrate}bps (${test2.quality}), 大小: ${test2.fileSize}字节`);
    } else {
        console.log(`     错误: ${test2.error}`);
    }
    
    return {
        config: config.name,
        sourceConfig,
        test1,
        test2
    };
}

/**
 * 主测试函数
 */
async function main() {
    console.log('🚀 开始配置效果验证测试');
    console.log('测试目标: 验证 ENABLE_LOCAL_VIP、FOLLOW_SOURCE_ORDER、BLOCK_ADS 配置的实际效果');
    
    const results = [];
    
    try {
        for (const config of configs) {
            const result = await runConfigTest(config);
            results.push(result);
            
            // 测试间隔
            await sleep(2000);
        }
        
        // 输出对比结果
        console.log('\n📊 测试结果对比:');
        console.log('=' .repeat(100));
        
        results.forEach((result, index) => {
            console.log(`\n${index + 1}. ${result.config}`);
            if (result.sourceConfig.success) {
                console.log(`   配置: VIP=${result.sourceConfig.enableLocalVip}, 顺序=${result.sourceConfig.followSourceOrder}, 无损=${result.sourceConfig.enableFlac}`);
            }
            console.log(`   多音源测试: ${result.test1.success ? '成功' : '失败'} ${result.test1.success ? `(${result.test1.source}, ${result.test1.bitrate}bps)` : `(${result.test1.error})`}`);
            console.log(`   单音源测试: ${result.test2.success ? '成功' : '失败'} ${result.test2.success ? `(${result.test2.source}, ${result.test2.bitrate}bps)` : `(${result.test2.error})`}`);
        });
        
        // 分析差异
        console.log('\n🔍 配置效果分析:');
        console.log('=' .repeat(50));
        
        const vipTrue = results.filter(r => r.sourceConfig.success && r.sourceConfig.enableLocalVip === true);
        const vipFalse = results.filter(r => r.sourceConfig.success && r.sourceConfig.enableLocalVip === false);
        
        if (vipTrue.length > 0 && vipFalse.length > 0) {
            console.log('📈 ENABLE_LOCAL_VIP 效果对比:');
            console.log(`   VIP=true 平均音质: ${vipTrue.filter(r => r.test1.success).map(r => r.test1.bitrate).join(', ')}bps`);
            console.log(`   VIP=false 平均音质: ${vipFalse.filter(r => r.test1.success).map(r => r.test1.bitrate).join(', ')}bps`);
        }
        
        const orderTrue = results.filter(r => r.sourceConfig.success && r.sourceConfig.followSourceOrder === true);
        const orderFalse = results.filter(r => r.sourceConfig.success && r.sourceConfig.followSourceOrder === false);
        
        if (orderTrue.length > 0 && orderFalse.length > 0) {
            console.log('\n📋 FOLLOW_SOURCE_ORDER 效果对比:');
            console.log(`   顺序=true 使用音源: ${orderTrue.filter(r => r.test1.success).map(r => r.test1.source).join(', ')}`);
            console.log(`   顺序=false 使用音源: ${orderFalse.filter(r => r.test1.success).map(r => r.test1.source).join(', ')}`);
        }
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
    }
    
    console.log('\n✅ 测试完成');
}

// 运行测试
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main, runConfigTest, testUnlock };
