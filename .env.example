# ===================================================================
# 音乐解锁服务后端环境配置文件示例
# Music Unlock Service Backend Environment Configuration Example
# ===================================================================

# -------------------------------------------------------------------
# 基础服务配置 (Basic Service Configuration)
# -------------------------------------------------------------------

# 服务运行端口 (Service Port)
PORT=3000

# 服务运行环境 (Runtime Environment)
NODE_ENV=development

# 服务主机地址 (Service Host)
HOST=localhost

# -------------------------------------------------------------------
# 日志系统配置 (Logging System Configuration)
# -------------------------------------------------------------------

# 日志级别 (Log Level)
LOG_LEVEL=info

# 启用文件日志 (Enable File Logging)
LOG_FILE_ENABLED=true

# 启用控制台日志 (Enable Console Logging)
LOG_CONSOLE_ENABLED=true

# -------------------------------------------------------------------
# 网络超时配置 (Network Timeout Configuration)
# -------------------------------------------------------------------

# 解锁请求超时时间 (Unlock Request Timeout)
UNLOCK_TIMEOUT=30000

# 音源测试超时时间 (Source Test Timeout)
SOURCE_TEST_TIMEOUT=10000

# API请求超时时间 (API Request Timeout)
API_REQUEST_TIMEOUT=30000

# -------------------------------------------------------------------
# 性能配置 (Performance Configuration)
# -------------------------------------------------------------------

# 批量处理并发数 (Batch Concurrency)
BATCH_CONCURRENCY=5

# 最大重试次数 (Max Retries)
MAX_RETRIES=3

# 重试延迟时间 (Retry Delay)
RETRY_DELAY=1000

# -------------------------------------------------------------------
# 安全配置 (Security Configuration)
# -------------------------------------------------------------------

# 请求频率限制 (Rate Limiting)
RATE_LIMIT_MAX_REQUESTS=100

# 频率限制时间窗口 (Rate Limit Window)
RATE_LIMIT_WINDOW_MS=900000

# CORS跨域配置 (CORS Configuration)
# 生产环境请设置具体域名，如: https://yourdomain.com
CORS_ORIGIN=*

# 会话密钥 (Session Secret)
# 生产环境必须设置强密码
SESSION_SECRET=change-this-in-production

# 最大请求大小 (Max Request Size)
MAX_REQUEST_SIZE=10mb

# -------------------------------------------------------------------
# 音乐服务配置 (Music Service Configuration)
# -------------------------------------------------------------------

# UnblockNeteaseMusic 音源配置 (Music Sources Configuration)
MUSIC_SOURCES=migu,kuwo,qq,kugou,joox,youtube

# 启用无损音质 (Enable FLAC Quality)
ENABLE_FLAC=true

# 启用本地VIP (Enable Local VIP)
ENABLE_LOCAL_VIP=true

# 音源顺序策略 (Follow Source Order)
FOLLOW_SOURCE_ORDER=false

# 屏蔽广告 (Block Ads)
BLOCK_ADS=true

# -------------------------------------------------------------------
# 音源认证配置 (Music Source Authentication)
# -------------------------------------------------------------------

# 网易云音乐Cookie (Netease Cookie)
NETEASE_COOKIE=

# QQ音乐Cookie (QQ Music Cookie)
QQ_COOKIE=

# 咪咕音乐Cookie (Migu Cookie)
MIGU_COOKIE=

# JOOX音乐Cookie (JOOX Cookie)
JOOX_COOKIE=

# YouTube API密钥 (YouTube API Key)
YOUTUBE_KEY=

# -------------------------------------------------------------------
# API限制配置 (API Limits Configuration)
# -------------------------------------------------------------------

# 批量操作最大数量 (Max Batch Size)
MAX_BATCH_SIZE=20

# 最大搜索结果数 (Max Search Results)
MAX_SEARCH_RESULTS=50

# 搜索关键词最大长度 (Max Keyword Length)
MAX_KEYWORD_LENGTH=100

# 请求超时时间 (Request Timeout)
REQUEST_TIMEOUT=30000

# -------------------------------------------------------------------
# 缓存配置 (Cache Configuration)
# -------------------------------------------------------------------

# 元数据缓存时间 (Metadata Cache TTL)
CACHE_METADATA_TTL=3600

# 搜索结果缓存时间 (Search Cache TTL)
CACHE_SEARCH_TTL=1800

# 解锁结果缓存时间 (Unlock Cache TTL)
CACHE_UNLOCK_TTL=7200

# 是否启用缓存 (Enable Cache)
CACHE_ENABLED=true
