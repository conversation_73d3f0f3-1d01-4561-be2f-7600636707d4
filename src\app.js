/**
 * 音乐解锁服务主应用入口
 * 基于Express.js和UnblockNeteaseMusic的音乐解锁服务后端
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

// 导入配置和工具
const config = require('./config/config');
const { logger, requestLogger } = require('./middleware/logger');
const { errorHandler, notFoundHandler, setupProcessErrorHandlers } = require('./middleware/errorHandler');


// 设置进程级错误处理
setupProcessErrorHandlers();

// 创建Express应用
const app = express();

// 信任代理（用于获取真实IP）
app.set('trust proxy', 1);

// 安全中间件
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ['\'self\''],
            styleSrc: ['\'self\'', '\'unsafe-inline\''], // 保留CSS内联样式支持
            scriptSrc: ['\'self\''], // 移除unsafe-inline，提高安全性
            imgSrc: ['\'self\'', 'data:', 'https:'],
            connectSrc: ['\'self\''], // 允许AJAX请求
            fontSrc: ['\'self\''], // 允许字体加载
        },
    },
}));

// CORS配置
app.use(cors({
    origin: config.security.corsOrigin,
    credentials: true,
    methods: ['GET', 'POST', 'OPTIONS'], // 只允许必要的HTTP方法
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    maxAge: 86400 // 预检请求缓存24小时
}));

// 请求体解析中间件 - 使用配置化的限制
app.use(express.json({ limit: config.security.maxRequestSize }));
app.use(express.urlencoded({ extended: true, limit: config.security.maxRequestSize }));

// 静态文件服务已移除 - 项目现为纯后端API服务

// 请求超时中间件
app.use((req, _res, next) => {
    req.setTimeout(config.limits.requestTimeout, () => {
        const error = new Error('请求超时');
        error.statusCode = 408;
        next(error);
    });
    next();
});

// 请求日志中间件
app.use(requestLogger);

// API限流中间件
const limiter = rateLimit({
    windowMs: config.security.rateLimitWindowMs,
    max: config.security.rateLimitMaxRequests,
    message: {
        code: 429,
        message: '请求过于频繁，请稍后再试',
        timestamp: new Date().toISOString()
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use('/music', limiter);

// 根路径 - 统一的API文档功能
app.get('/', (_req, res) => {
    const packageInfo = require('../package.json');

    // 构建新的统一API文档格式
    const apiData = {
        状态码: 200,
        消息: '音乐解锁服务API文档',
        时间戳: new Date().toISOString(),
        版本: packageInfo.version,
        'API文档': [
            {
                '快速示例': {
                    '单首歌曲解锁': '/music/unlock?sources=qq,migu&songs=418602084',
                    '批量歌曲解锁': '/music/unlock?sources=qq,migu,kuwo&songs=418602084,123456,186016',
                    '获取音源管理信息': '/music/source'
                }
            },
            {
                '请求方法': 'GET',
                '接口路径': '/music/unlock',
                '接口描述': '音乐解锁服务,批量音乐解锁接口，支持多音源并行解锁',
                '请求参数': {
                    sources: {
                        '参数名称': '音源列表',
                        '参数类型': '字符串',
                        '是否必需': '是',
                        '参数描述': '音源ID列表，逗号分隔',
                        '可选值': 'qq,migu,kuwo,kugou,joox,youtube',
                        '示例值': 'qq,migu,kuwo'
                    },
                    songs: {
                        '参数名称': '歌曲列表',
                        '参数类型': '字符串',
                        '是否必需': '是',
                        '参数描述': '歌曲ID列表，逗号分隔',
                        '示例值': '418602084,123456'
                    }
                }
            },
            {
                '请求方法': 'GET',
                '接口路径': '/music/source',
                '接口描述': '音源管理服务,获取整合的音源管理信息，包含音源列表、统计数据和配置信息',
                '请求参数': {},
                '返回数据': {
                    '音源总数': '所有音源数量',
                    '已启用音源': '当前启用的音源数量',
                    '已禁用音源': '当前禁用的音源数量',
                    '音源优先级顺序': '按优先级排序的音源详细信息列表'
                },

            }
        ]
    };

    // 直接返回新格式的数据，不使用success包装函数
    res.json(apiData);
});


// 统一音乐服务路由
app.use('/music', require('./routes/musicRoutes'));
app.use('/music', require('./routes/sourceRoutes'));

// 404错误处理
app.use(notFoundHandler);

// 全局错误处理中间件
app.use(errorHandler);

// 启动服务器
const server = app.listen(config.server.port, config.server.host, () => {
    logger.info('🎵 音乐解锁服务启动成功', {
        host: config.server.host,
        port: config.server.port,
        environment: config.server.env,
        pid: process.pid
    });
  
    logger.info(`🔍 服务状态和API文档: http://${config.server.host}:${config.server.port}/`);
    logger.info(`🎯 API基础地址: http://${config.server.host}:${config.server.port}/music`);
    logger.info('📝 使用独立的HTML测试工具进行API测试');
});

// 优雅关闭处理
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

function gracefulShutdown(signal) {
    logger.info(`收到${signal}信号，开始优雅关闭服务器...`);
  
    server.close((err) => {
        if (err) {
            logger.error('服务器关闭时发生错误:', err);
            process.exit(1);
        }
    
        logger.info('服务器已优雅关闭');
        process.exit(0);
    });
  
    // 强制关闭超时
    setTimeout(() => {
        logger.error('强制关闭服务器');
        process.exit(1);
    }, 10000);
}

module.exports = app;
