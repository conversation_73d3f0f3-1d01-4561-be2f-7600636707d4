/**
 * 音乐解锁服务集成测试脚本
 * 测试完整的业务流程和系统集成
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// 测试配置
const BASE_URL = 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api`;

// 测试数据
const TEST_DATA = {
    songs: [
        { id: '418602084', name: '稻香', artist: '周杰伦' },
        { id: '186016', name: '不能说的秘密', artist: '周杰伦' },
        { id: '185868', name: '青花瓷', artist: '周杰伦' }
    ],
    searchKeywords: ['周杰伦', '稻香', '青花瓷', '不能说的秘密'],
    sources: ['qq', 'kugou', 'kuwo', 'migu']
};

/**
 * 集成测试工具类
 */
class IntegrationTester {
    constructor() {
        this.testResults = [];
        this.errors = [];
    }

    /**
     * 记录测试结果
     */
    recordTest(testName, success, details = {}) {
        const result = {
            testName,
            success,
            timestamp: new Date().toISOString(),
            ...details
        };
        
        this.testResults.push(result);
        
        if (success) {
            console.log(`✅ ${testName}`);
        } else {
            console.log(`❌ ${testName}: ${details.error || '测试失败'}`);
            this.errors.push(result);
        }
        
        return result;
    }

    /**
     * HTTP请求封装
     */
    async request(method, url, data = null) {
        try {
            const config = {
                method,
                url,
                timeout: 10000,
                ...(data && { data })
            };

            const response = await axios(config);
            return {
                success: true,
                status: response.status,
                data: response.data
            };
        } catch (error) {
            return {
                success: false,
                status: error.response?.status || 0,
                error: error.message,
                data: error.response?.data
            };
        }
    }

    /**
     * 测试1: 系统健康检查
     */
    async testSystemHealth() {
        console.log('\n🏥 测试系统健康状态...');
        
        const result = await this.request('GET', `${BASE_URL}/health`);
        
        if (result.success && result.data.code === 200 && result.data.data.status === 'healthy') {
            return this.recordTest('系统健康检查', true, {
                responseTime: result.responseTime,
                status: result.data.data.status
            });
        } else {
            return this.recordTest('系统健康检查', false, {
                error: result.error || '健康检查失败'
            });
        }
    }

    /**
     * 测试2: API基础功能
     */
    async testApiBasics() {
        console.log('\n📋 测试API基础功能...');
        
        // 测试API信息
        const apiInfoResult = await this.request('GET', API_BASE);
        const apiInfoSuccess = apiInfoResult.success &&
                              apiInfoResult.data.name === '音乐解锁服务API';

        this.recordTest('API信息获取', apiInfoSuccess, {
            version: apiInfoResult.data?.version
        });

        // 测试API文档
        const docsResult = await this.request('GET', `${API_BASE}/docs`);
        const docsSuccess = docsResult.success &&
                           docsResult.data.title === '音乐解锁服务API文档';

        this.recordTest('API文档获取', docsSuccess, {
            endpointCount: docsResult.data?.endpoints?.length
        });

        return apiInfoSuccess && docsSuccess;
    }

    /**
     * 测试3: 歌曲信息完整流程
     */
    async testSongInfoWorkflow() {
        console.log('\n🎵 测试歌曲信息完整流程...');
        
        const testSong = TEST_DATA.songs[0];
        let allSuccess = true;

        // 1. 获取歌曲基本信息
        const songInfoResult = await this.request('GET', `${API_BASE}/song/${testSong.id}`);
        const songInfoSuccess = songInfoResult.success &&
                               songInfoResult.data.code === 200 &&
                               songInfoResult.data.data.id === parseInt(testSong.id);

        this.recordTest(`获取歌曲信息 - ${testSong.name}`, songInfoSuccess);
        allSuccess = allSuccess && songInfoSuccess;

        // 2. 获取歌曲元数据
        const metadataResult = await this.request('GET', `${API_BASE}/metadata/${testSong.id}`);
        const metadataSuccess = metadataResult.success &&
                               metadataResult.data.code === 200;

        this.recordTest(`获取歌曲元数据 - ${testSong.name}`, metadataSuccess);
        allSuccess = allSuccess && metadataSuccess;

        // 3. 检查歌曲可用性
        const availabilityResult = await this.request('HEAD', `${API_BASE}/song/${testSong.id}`);
        const availabilitySuccess = availabilityResult.success;
        
        this.recordTest(`检查歌曲可用性 - ${testSong.name}`, availabilitySuccess);
        allSuccess = allSuccess && availabilitySuccess;

        // 4. 获取歌曲音源
        const sourcesResult = await this.request('GET', `${API_BASE}/song/${testSong.id}/sources`);
        const sourcesSuccess = sourcesResult.success &&
                              sourcesResult.data.code === 200;

        this.recordTest(`获取歌曲音源 - ${testSong.name}`, sourcesSuccess, {
            sourceCount: sourcesResult.data?.data?.sources?.length || 0
        });
        allSuccess = allSuccess && sourcesSuccess;

        return allSuccess;
    }

    /**
     * 测试4: 搜索功能完整流程
     */
    async testSearchWorkflow() {
        console.log('\n🔍 测试搜索功能完整流程...');
        
        let allSuccess = true;

        // 1. 关键词搜索
        const keywordSearchResult = await this.request('POST', `${API_BASE}/search`, {
            type: 'keyword',
            query: TEST_DATA.searchKeywords[0],
            page: 1,
            pageSize: 10
        });
        const keywordSearchSuccess = keywordSearchResult.success && 
                                    keywordSearchResult.data.success && 
                                    Array.isArray(keywordSearchResult.data.data.songs);
        
        this.recordTest(`关键词搜索 - ${TEST_DATA.searchKeywords[0]}`, keywordSearchSuccess, {
            resultCount: keywordSearchResult.data?.data?.songs?.length
        });
        allSuccess = allSuccess && keywordSearchSuccess;

        // 2. ID搜索
        const idSearchResult = await this.request('GET', `${API_BASE}/search/id/${TEST_DATA.songs[0].id}`);
        const idSearchSuccess = idSearchResult.success && 
                               idSearchResult.data.success;
        
        this.recordTest(`ID搜索 - ${TEST_DATA.songs[0].id}`, idSearchSuccess);
        allSuccess = allSuccess && idSearchSuccess;

        // 3. 批量搜索
        const batchSearchResult = await this.request('POST', `${API_BASE}/search/batch`, {
            queries: TEST_DATA.songs.slice(0, 2).map(s => s.id)
        });
        const batchSearchSuccess = batchSearchResult.success && 
                                  batchSearchResult.data.success && 
                                  Array.isArray(batchSearchResult.data.data.results);
        
        this.recordTest('批量搜索', batchSearchSuccess, {
            resultCount: batchSearchResult.data?.data?.results?.length
        });
        allSuccess = allSuccess && batchSearchSuccess;

        // 4. 搜索建议
        const suggestResult = await this.request('GET', `${API_BASE}/search/suggest?q=周杰`);
        const suggestSuccess = suggestResult.success && 
                              suggestResult.data.success && 
                              Array.isArray(suggestResult.data.data.suggestions);
        
        this.recordTest('搜索建议', suggestSuccess, {
            suggestionCount: suggestResult.data?.data?.suggestions?.length
        });
        allSuccess = allSuccess && suggestSuccess;

        // 5. 热门搜索
        const trendingResult = await this.request('GET', `${API_BASE}/search/trending`);
        const trendingSuccess = trendingResult.success && 
                               trendingResult.data.success && 
                               Array.isArray(trendingResult.data.data.trending);
        
        this.recordTest('热门搜索', trendingSuccess, {
            trendingCount: trendingResult.data?.data?.trending?.length
        });
        allSuccess = allSuccess && trendingSuccess;

        return allSuccess;
    }

    /**
     * 测试5: 解锁功能完整流程
     */
    async testUnlockWorkflow() {
        console.log('\n🔓 测试解锁功能完整流程...');
        
        let allSuccess = true;
        const testSongIds = TEST_DATA.songs.slice(0, 2).map(s => parseInt(s.id));

        // 1. 单首歌曲解锁
        const singleUnlockResult = await this.request('POST', `${API_BASE}/unlock/${testSongIds[0]}`, {
            minBitrate: 128000
        });
        const singleUnlockSuccess = singleUnlockResult.success && 
                                   singleUnlockResult.data.success;
        
        this.recordTest(`单首歌曲解锁 - ${testSongIds[0]}`, singleUnlockSuccess);
        allSuccess = allSuccess && singleUnlockSuccess;

        // 2. 批量解锁
        const batchUnlockResult = await this.request('POST', `${API_BASE}/unlock`, {
            songIds: testSongIds,
            minBitrate: 128000,
            detailed: true
        });
        const batchUnlockSuccess = batchUnlockResult.success && 
                                  batchUnlockResult.data.success && 
                                  Array.isArray(batchUnlockResult.data.data.results);
        
        this.recordTest('批量解锁', batchUnlockSuccess, {
            processedCount: batchUnlockResult.data?.data?.results?.length
        });
        allSuccess = allSuccess && batchUnlockSuccess;

        // 3. 快速解锁
        const quickUnlockResult = await this.request('POST', `${API_BASE}/unlock/quick`, {
            songIds: [testSongIds[0]]
        });
        const quickUnlockSuccess = quickUnlockResult.success && 
                                  quickUnlockResult.data.success;
        
        this.recordTest('快速解锁', quickUnlockSuccess);
        allSuccess = allSuccess && quickUnlockSuccess;

        // 4. 检查解锁状态
        const statusResult = await this.request('GET', `${API_BASE}/unlock/status/${testSongIds[0]}`);
        const statusSuccess = statusResult.success && 
                             statusResult.data.success;
        
        this.recordTest(`检查解锁状态 - ${testSongIds[0]}`, statusSuccess);
        allSuccess = allSuccess && statusSuccess;

        // 5. 批量检查解锁状态
        const batchStatusResult = await this.request('POST', `${API_BASE}/unlock/status/batch`, {
            songIds: testSongIds
        });
        const batchStatusSuccess = batchStatusResult.success && 
                                  batchStatusResult.data.success && 
                                  Array.isArray(batchStatusResult.data.data.results);
        
        this.recordTest('批量检查解锁状态', batchStatusSuccess, {
            checkedCount: batchStatusResult.data?.data?.results?.length
        });
        allSuccess = allSuccess && batchStatusSuccess;

        return allSuccess;
    }

    /**
     * 测试6: 音源管理完整流程
     */
    async testSourceManagementWorkflow() {
        console.log('\n🎯 测试音源管理完整流程...');
        
        let allSuccess = true;

        // 1. 获取音源列表
        const sourcesResult = await this.request('GET', `${API_BASE}/sources`);
        const sourcesSuccess = sourcesResult.success && 
                              sourcesResult.data.success && 
                              Array.isArray(sourcesResult.data.data.sources);
        
        this.recordTest('获取音源列表', sourcesSuccess, {
            sourceCount: sourcesResult.data?.data?.sources?.length
        });
        allSuccess = allSuccess && sourcesSuccess;

        // 2. 获取音源详情
        const sourceDetailResult = await this.request('GET', `${API_BASE}/sources/qq`);
        const sourceDetailSuccess = sourceDetailResult.success && 
                                   sourceDetailResult.data.success;
        
        this.recordTest('获取音源详情 - QQ音乐', sourceDetailSuccess);
        allSuccess = allSuccess && sourceDetailSuccess;

        // 3. 测试单个音源
        const testSourceResult = await this.request('POST', `${API_BASE}/sources/qq/test`, {});
        const testSourceSuccess = testSourceResult.success && 
                                 testSourceResult.data.success;
        
        this.recordTest('测试单个音源 - QQ音乐', testSourceSuccess);
        allSuccess = allSuccess && testSourceSuccess;

        // 4. 批量测试音源
        const batchTestResult = await this.request('POST', `${API_BASE}/sources/test/batch`, {
            sourceIds: TEST_DATA.sources.slice(0, 2)
        });
        const batchTestSuccess = batchTestResult.success && 
                                batchTestResult.data.success && 
                                Array.isArray(batchTestResult.data.data.results);
        
        this.recordTest('批量测试音源', batchTestSuccess, {
            testedCount: batchTestResult.data?.data?.results?.length
        });
        allSuccess = allSuccess && batchTestSuccess;

        // 5. 获取音源统计
        const statsResult = await this.request('GET', `${API_BASE}/sources/stats`);
        const statsSuccess = statsResult.success && 
                            statsResult.data.success;
        
        this.recordTest('获取音源统计', statsSuccess);
        allSuccess = allSuccess && statsSuccess;

        // 6. 获取音源配置
        const configResult = await this.request('GET', `${API_BASE}/sources/config`);
        const configSuccess = configResult.success && 
                             configResult.data.success;
        
        this.recordTest('获取音源配置', configSuccess);
        allSuccess = allSuccess && configSuccess;

        return allSuccess;
    }

    /**
     * 测试7: 错误处理和边界情况
     */
    async testErrorHandling() {
        console.log('\n⚠️ 测试错误处理和边界情况...');
        
        let allSuccess = true;

        // 1. 无效歌曲ID
        const invalidIdResult = await this.request('GET', `${API_BASE}/song/invalid`);
        const invalidIdSuccess = !invalidIdResult.success && invalidIdResult.status === 400;
        
        this.recordTest('无效歌曲ID处理', invalidIdSuccess);
        allSuccess = allSuccess && invalidIdSuccess;

        // 2. 不存在的路径
        const notFoundResult = await this.request('GET', `${API_BASE}/nonexistent`);
        const notFoundSuccess = !notFoundResult.success && notFoundResult.status === 404;
        
        this.recordTest('404错误处理', notFoundSuccess);
        allSuccess = allSuccess && notFoundSuccess;

        // 3. 缺少必需参数
        const missingParamResult = await this.request('POST', `${API_BASE}/search`, {});
        const missingParamSuccess = !missingParamResult.success && missingParamResult.status === 400;
        
        this.recordTest('缺少参数错误处理', missingParamSuccess);
        allSuccess = allSuccess && missingParamSuccess;

        return allSuccess;
    }

    /**
     * 生成测试报告
     */
    generateReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.success).length;
        const failedTests = totalTests - passedTests;
        const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

        return {
            totalTests,
            passedTests,
            failedTests,
            successRate,
            errors: this.errors,
            details: this.testResults
        };
    }
}

/**
 * 运行集成测试
 */
async function runIntegrationTests() {
    console.log('🎵 音乐解锁服务集成测试开始\n');
    console.log('='.repeat(60));
    
    const tester = new IntegrationTester();
    const startTime = performance.now();

    try {
        // 执行所有测试
        await tester.testSystemHealth();
        await tester.testApiBasics();
        await tester.testSongInfoWorkflow();
        await tester.testSearchWorkflow();
        await tester.testUnlockWorkflow();
        await tester.testSourceManagementWorkflow();
        await tester.testErrorHandling();

        const endTime = performance.now();
        const duration = endTime - startTime;

        // 生成报告
        const report = tester.generateReport();

        console.log('\n📊 集成测试报告');
        console.log('='.repeat(60));
        console.log(`测试总数: ${report.totalTests}`);
        console.log(`通过测试: ${report.passedTests}`);
        console.log(`失败测试: ${report.failedTests}`);
        console.log(`成功率: ${report.successRate.toFixed(2)}%`);
        console.log(`测试时长: ${(duration / 1000).toFixed(2)}秒`);
        console.log('='.repeat(60));

        if (report.failedTests > 0) {
            console.log('\n❌ 失败的测试:');
            report.errors.forEach(error => {
                console.log(`   - ${error.testName}: ${error.error || '未知错误'}`);
            });
        }

        const passed = report.successRate >= 90; // 90%通过率
        if (passed) {
            console.log('\n✅ 集成测试通过！');
        } else {
            console.log('\n❌ 集成测试未通过，需要修复问题。');
        }

        return report;

    } catch (error) {
        console.error('\n💥 集成测试过程中发生错误:', error.message);
        throw error;
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    runIntegrationTests()
        .then(() => {
            console.log('\n🎉 集成测试完成！');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 集成测试失败:', error.message);
            process.exit(1);
        });
}

module.exports = { IntegrationTester, runIntegrationTests };
